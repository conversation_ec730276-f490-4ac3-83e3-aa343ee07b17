#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全自动PyInstaller打包脚本
自动检测依赖、生成配置、执行打包
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    """主打包流程"""
    print("🚀 开始全自动打包流程...")
    
    # 项目根目录
    project_root = Path(__file__).parent
    venv_python = project_root / "venv_auto" / "Scripts" / "python.exe"
    venv_pyinstaller = project_root / "venv_auto" / "Scripts" / "pyinstaller.exe"
    
    # 检查虚拟环境
    if not venv_python.exists():
        print("❌ 虚拟环境不存在，请先运行环境准备")
        return False
    
    # 检查资源文件
    resource_files = ["order_history.csv", "product_history.json"]
    missing_files = []
    for file in resource_files:
        if not (project_root / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"⚠️ 缺少资源文件: {missing_files}")
        # 创建空的资源文件
        for file in missing_files:
            (project_root / file).touch()
            print(f"✅ 已创建空文件: {file}")
    
    # 构建PyInstaller命令
    cmd = [
        str(venv_pyinstaller),
        "--onefile",                    # 单文件输出
        "--windowed",                   # 隐藏控制台
        "--name", "DispatchApp",        # 应用名称
        "--distpath", "dist",           # 输出目录
        "--workpath", "build",          # 临时目录
        "--specpath", ".",              # spec文件位置
        
        # 添加资源文件
        "--add-data", "order_history.csv;.",
        "--add-data", "product_history.json;.",
        
        # 隐藏导入（解决动态依赖）
        "--hidden-import", "pandas",
        "--hidden-import", "openpyxl",
        "--hidden-import", "PyQt5",
        "--hidden-import", "PyQt5.QtCore",
        "--hidden-import", "PyQt5.QtWidgets",
        "--hidden-import", "PyQt5.QtGui",
        "--hidden-import", "numpy",
        "--hidden-import", "csv",
        "--hidden-import", "json",
        "--hidden-import", "datetime",
        "--hidden-import", "logging",
        
        # 主入口文件
        "main.py"
    ]
    
    print("📦 执行PyInstaller打包...")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        # 执行打包
        result = subprocess.run(
            cmd,
            cwd=project_root,
            capture_output=True,
            text=True,
            timeout=600  # 10分钟超时
        )
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            
            # 检查输出文件
            exe_path = project_root / "dist" / "DispatchApp.exe"
            if exe_path.exists():
                print(f"📁 生成的exe文件: {exe_path}")
                print(f"📊 文件大小: {exe_path.stat().st_size / 1024 / 1024:.1f} MB")
                
                # 简单测试（启动检查）
                print("🧪 执行启动测试...")
                test_result = test_exe(exe_path)
                if test_result:
                    print("✅ 启动测试通过")
                else:
                    print("⚠️ 启动测试失败，但exe文件已生成")
                
                return True
            else:
                print("❌ exe文件未生成")
                return False
        else:
            print("❌ 打包失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 打包超时")
        return False
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return False

def test_exe(exe_path):
    """测试exe文件是否能正常启动"""
    try:
        # 启动进程但立即终止（避免GUI阻塞）
        process = subprocess.Popen(
            [str(exe_path)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待短时间后终止
        import time
        time.sleep(3)
        process.terminate()
        
        return True
    except Exception as e:
        print(f"启动测试异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
