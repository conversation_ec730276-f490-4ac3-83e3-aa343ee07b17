#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面派单程序主入口（含日志、高DPI支持、插件路径处理）
"""
import sys
import os
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QLibraryInfo
from ui_main import MainWindow  # 确保 ui_main.py 存在且路径正确

# 配置日志：同时输出到控制台和文件
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("dispatch_log.log"),  # 日志文件
        logging.StreamHandler(sys.stdout)          # 控制台输出
    ]
)

def main():
    """主函数：初始化应用、加载窗口、处理异常"""
    logger = logging.getLogger(__name__)
    logger.info("程序启动")

    # 【可选】处理 PyQt5 插件路径（解决打包后插件缺失问题）
    try:
        plugin_path = QLibraryInfo.location(QLibraryInfo.PluginsPath)
        if os.path.exists(plugin_path):
            os.environ["QT_QPA_PLATFORM_PLUGIN_PATH"] = plugin_path
            logger.info(f"已设置插件路径: {plugin_path}")
        else:
            logger.warning(f"插件路径不存在: {plugin_path}，依赖系统默认路径")
    except Exception as e:
        logger.warning(f"插件路径设置失败: {e}")

    # 初始化 PyQt 应用，启用高DPI适配
    app = QApplication(sys.argv)
    app.setAttribute(Qt.AA_EnableHighDpiScaling)  # 适配高分辨率屏幕

    # 加载主窗口（捕获初始化异常）
    try:
        window = MainWindow()
        window.show()
        logger.info("主窗口加载完成，启动事件循环")
        sys.exit(app.exec_())
    except Exception as e:
        logger.error(f"主窗口加载失败: {e}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    main()