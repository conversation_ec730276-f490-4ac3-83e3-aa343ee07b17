(['D:\\work\\python_work\\Desktop Order Dispatch System\\main.py'],
 ['D:\\work\\python_work\\Desktop Order Dispatch System'],
 ['pandas',
  'openpyxl',
  'PyQt5',
  'PyQt5.QtCore',
  'PyQt5.QtWidgets',
  'PyQt5.QtGui',
  'numpy',
  'csv',
  'json',
  'datetime',
  'logging'],
 [('D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('order_history.csv',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\order_history.csv',
   'DATA'),
  ('product_history.json',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\product_history.json',
   'DATA')],
 '3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\main.py',
   'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'D:\\soft\\python3.10\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\soft\\python3.10\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('typing', 'D:\\soft\\python3.10\\lib\\typing.py', 'PYMODULE'),
  ('contextlib', 'D:\\soft\\python3.10\\lib\\contextlib.py', 'PYMODULE'),
  ('importlib._abc',
   'D:\\soft\\python3.10\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\soft\\python3.10\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\soft\\python3.10\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\soft\\python3.10\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\soft\\python3.10\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\soft\\python3.10\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\soft\\python3.10\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\soft\\python3.10\\lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\soft\\python3.10\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\soft\\python3.10\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib', 'D:\\soft\\python3.10\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('email.iterators',
   'D:\\soft\\python3.10\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\soft\\python3.10\\lib\\email\\generator.py',
   'PYMODULE'),
  ('copy', 'D:\\soft\\python3.10\\lib\\copy.py', 'PYMODULE'),
  ('random', 'D:\\soft\\python3.10\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\soft\\python3.10\\lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\soft\\python3.10\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\soft\\python3.10\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\soft\\python3.10\\lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\soft\\python3.10\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\soft\\python3.10\\lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\soft\\python3.10\\lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'D:\\soft\\python3.10\\lib\\bisect.py', 'PYMODULE'),
  ('_strptime', 'D:\\soft\\python3.10\\lib\\_strptime.py', 'PYMODULE'),
  ('calendar', 'D:\\soft\\python3.10\\lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\soft\\python3.10\\lib\\argparse.py', 'PYMODULE'),
  ('shutil', 'D:\\soft\\python3.10\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\soft\\python3.10\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\soft\\python3.10\\lib\\gzip.py', 'PYMODULE'),
  ('_compression', 'D:\\soft\\python3.10\\lib\\_compression.py', 'PYMODULE'),
  ('struct', 'D:\\soft\\python3.10\\lib\\struct.py', 'PYMODULE'),
  ('lzma', 'D:\\soft\\python3.10\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\soft\\python3.10\\lib\\bz2.py', 'PYMODULE'),
  ('fnmatch', 'D:\\soft\\python3.10\\lib\\fnmatch.py', 'PYMODULE'),
  ('gettext', 'D:\\soft\\python3.10\\lib\\gettext.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\soft\\python3.10\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\soft\\python3.10\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\soft\\python3.10\\lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\soft\\python3.10\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders',
   'D:\\soft\\python3.10\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\soft\\python3.10\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\soft\\python3.10\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\soft\\python3.10\\lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\soft\\python3.10\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\soft\\python3.10\\lib\\email\\utils.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\soft\\python3.10\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('urllib.parse', 'D:\\soft\\python3.10\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('socket', 'D:\\soft\\python3.10\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\soft\\python3.10\\lib\\selectors.py', 'PYMODULE'),
  ('quopri', 'D:\\soft\\python3.10\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'D:\\soft\\python3.10\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'D:\\soft\\python3.10\\lib\\optparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\soft\\python3.10\\lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'D:\\soft\\python3.10\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\soft\\python3.10\\lib\\py_compile.py', 'PYMODULE'),
  ('threading', 'D:\\soft\\python3.10\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\soft\\python3.10\\lib\\_threading_local.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\soft\\python3.10\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib', 'D:\\soft\\python3.10\\lib\\pathlib.py', 'PYMODULE'),
  ('email', 'D:\\soft\\python3.10\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\soft\\python3.10\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\soft\\python3.10\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\soft\\python3.10\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('tokenize', 'D:\\soft\\python3.10\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\soft\\python3.10\\lib\\token.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\soft\\python3.10\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('subprocess', 'D:\\soft\\python3.10\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\soft\\python3.10\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'D:\\soft\\python3.10\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'D:\\soft\\python3.10\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\soft\\python3.10\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\soft\\python3.10\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\soft\\python3.10\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\soft\\python3.10\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\soft\\python3.10\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\soft\\python3.10\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\soft\\python3.10\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\soft\\python3.10\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\soft\\python3.10\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\soft\\python3.10\\lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\soft\\python3.10\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\soft\\python3.10\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\soft\\python3.10\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\soft\\python3.10\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error', 'D:\\soft\\python3.10\\lib\\urllib\\error.py', 'PYMODULE'),
  ('xml.sax', 'D:\\soft\\python3.10\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client', 'D:\\soft\\python3.10\\lib\\http\\client.py', 'PYMODULE'),
  ('hmac', 'D:\\soft\\python3.10\\lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'D:\\soft\\python3.10\\lib\\tempfile.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'D:\\soft\\python3.10\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\soft\\python3.10\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\soft\\python3.10\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\soft\\python3.10\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\soft\\python3.10\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\soft\\python3.10\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\soft\\python3.10\\lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\soft\\python3.10\\lib\\dataclasses.py', 'PYMODULE'),
  ('inspect', 'D:\\soft\\python3.10\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\soft\\python3.10\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\soft\\python3.10\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\soft\\python3.10\\lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\soft\\python3.10\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\soft\\python3.10\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\soft\\python3.10\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\soft\\python3.10\\lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('datetime', 'D:\\soft\\python3.10\\lib\\datetime.py', 'PYMODULE'),
  ('json', 'D:\\soft\\python3.10\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\soft\\python3.10\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\soft\\python3.10\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\soft\\python3.10\\lib\\json\\scanner.py', 'PYMODULE'),
  ('csv', 'D:\\soft\\python3.10\\lib\\csv.py', 'PYMODULE'),
  ('numpy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('__future__', 'D:\\soft\\python3.10\\lib\\__future__.py', 'PYMODULE'),
  ('numpy._typing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('platform', 'D:\\soft\\python3.10\\lib\\platform.py', 'PYMODULE'),
  ('fileinput', 'D:\\soft\\python3.10\\lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('sysconfig', 'D:\\soft\\python3.10\\lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'D:\\soft\\python3.10\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\soft\\python3.10\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest', 'D:\\soft\\python3.10\\lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\soft\\python3.10\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\soft\\python3.10\\lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser', 'D:\\soft\\python3.10\\lib\\webbrowser.py', 'PYMODULE'),
  ('http.server', 'D:\\soft\\python3.10\\lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'D:\\soft\\python3.10\\lib\\socketserver.py', 'PYMODULE'),
  ('html', 'D:\\soft\\python3.10\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\soft\\python3.10\\lib\\html\\entities.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\soft\\python3.10\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\soft\\python3.10\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\soft\\python3.10\\lib\\tty.py', 'PYMODULE'),
  ('glob', 'D:\\soft\\python3.10\\lib\\glob.py', 'PYMODULE'),
  ('code', 'D:\\soft\\python3.10\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\soft\\python3.10\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\soft\\python3.10\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\soft\\python3.10\\lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\soft\\python3.10\\lib\\difflib.py', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('concurrent.futures',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\soft\\python3.10\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\soft\\python3.10\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'D:\\soft\\python3.10\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.util', 'D:\\soft\\python3.10\\lib\\unittest\\util.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\soft\\python3.10\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest', 'D:\\soft\\python3.10\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\soft\\python3.10\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio', 'D:\\soft\\python3.10\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'D:\\soft\\python3.10\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\soft\\python3.10\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\soft\\python3.10\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\soft\\python3.10\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\soft\\python3.10\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\soft\\python3.10\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\soft\\python3.10\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\soft\\python3.10\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\soft\\python3.10\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\soft\\python3.10\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'D:\\soft\\python3.10\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\soft\\python3.10\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\soft\\python3.10\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\soft\\python3.10\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\soft\\python3.10\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\soft\\python3.10\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\soft\\python3.10\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\soft\\python3.10\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\soft\\python3.10\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\soft\\python3.10\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\soft\\python3.10\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\soft\\python3.10\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main', 'D:\\soft\\python3.10\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner',
   'D:\\soft\\python3.10\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\soft\\python3.10\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\soft\\python3.10\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('pandas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('uuid', 'D:\\soft\\python3.10\\lib\\uuid.py', 'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('six',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('pytz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('sqlite3', 'D:\\soft\\python3.10\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'D:\\soft\\python3.10\\lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\soft\\python3.10\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom', 'D:\\soft\\python3.10\\lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('pandas.io.xml',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('stringprep', 'D:\\soft\\python3.10\\lib\\stringprep.py', 'PYMODULE'),
  ('_py_abc', 'D:\\soft\\python3.10\\lib\\_py_abc.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\soft\\python3.10\\lib\\tracemalloc.py', 'PYMODULE'),
  ('ui_main',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\ui_main.py',
   'PYMODULE'),
  ('excel_handler',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\excel_handler.py',
   'PYMODULE'),
  ('data_handler',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\data_handler.py',
   'PYMODULE'),
  ('logging', 'D:\\soft\\python3.10\\lib\\logging\\__init__.py', 'PYMODULE')],
 [('python310.dll', 'D:\\soft\\python3.10\\python310.dll', 'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\soft\\python3.10\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\soft\\python3.10\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\soft\\python3.10\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\soft\\python3.10\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\soft\\python3.10\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\soft\\python3.10\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\soft\\python3.10\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\soft\\python3.10\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\soft\\python3.10\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\soft\\python3.10\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\soft\\python3.10\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\soft\\python3.10\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\soft\\python3.10\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'D:\\soft\\python3.10\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\sip.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtNetwork.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\QtNetwork.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\soft\\python3.10\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\json.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reduction.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\reduction.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\join.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\soft\\python3.10\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\index.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\soft\\python3.10\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_sas.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\_sas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\io\\sas\\_byteswap.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\_byteswap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('winsound.pyd', 'D:\\soft\\python3.10\\DLLs\\winsound.pyd', 'EXTENSION'),
  ('PyQt5\\QtMultimedia.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\QtMultimedia.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\soft\\python3.10\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\soft\\python3.10\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\soft\\python3.10\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'D:\\soft\\python3.10\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\soft\\python3.10\\DLLs\\libffi-7.dll', 'BINARY'),
  ('python3.dll', 'D:\\soft\\python3.10\\python3.dll', 'BINARY'),
  ('pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\window\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('pandas\\_libs\\window\\MSVCP140.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\window\\MSVCP140.dll',
   'BINARY'),
  ('sqlite3.dll', 'D:\\soft\\python3.10\\DLLs\\sqlite3.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY')],
 [],
 [],
 [('order_history.csv',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\order_history.csv',
   'DATA'),
  ('product_history.json',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\product_history.json',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_hu.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_sk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_cs.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ru.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ja.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_fi.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_tr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_it.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ar.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_de.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ko.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_uk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_da.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_zh_TW.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_bg.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_ca.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_es.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_pl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_fr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtmultimedia_en.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtmultimedia_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\build\\DispatchApp\\base_library.zip',
   'DATA')],
 [('operator', 'D:\\soft\\python3.10\\lib\\operator.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\soft\\python3.10\\lib\\_weakrefset.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\soft\\python3.10\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\soft\\python3.10\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('re', 'D:\\soft\\python3.10\\lib\\re.py', 'PYMODULE'),
  ('keyword', 'D:\\soft\\python3.10\\lib\\keyword.py', 'PYMODULE'),
  ('io', 'D:\\soft\\python3.10\\lib\\io.py', 'PYMODULE'),
  ('genericpath', 'D:\\soft\\python3.10\\lib\\genericpath.py', 'PYMODULE'),
  ('codecs', 'D:\\soft\\python3.10\\lib\\codecs.py', 'PYMODULE'),
  ('locale', 'D:\\soft\\python3.10\\lib\\locale.py', 'PYMODULE'),
  ('traceback', 'D:\\soft\\python3.10\\lib\\traceback.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\soft\\python3.10\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\soft\\python3.10\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\soft\\python3.10\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\soft\\python3.10\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\soft\\python3.10\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\soft\\python3.10\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\soft\\python3.10\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\soft\\python3.10\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\soft\\python3.10\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\soft\\python3.10\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\soft\\python3.10\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\soft\\python3.10\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\soft\\python3.10\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\soft\\python3.10\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\soft\\python3.10\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem', 'D:\\soft\\python3.10\\lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs',
   'D:\\soft\\python3.10\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\soft\\python3.10\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\soft\\python3.10\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\soft\\python3.10\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\soft\\python3.10\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\soft\\python3.10\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\soft\\python3.10\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\soft\\python3.10\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\soft\\python3.10\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\soft\\python3.10\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\soft\\python3.10\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\soft\\python3.10\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\soft\\python3.10\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\soft\\python3.10\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\soft\\python3.10\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\soft\\python3.10\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\soft\\python3.10\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz', 'D:\\soft\\python3.10\\lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\soft\\python3.10\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\soft\\python3.10\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'D:\\soft\\python3.10\\lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312',
   'D:\\soft\\python3.10\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\soft\\python3.10\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\soft\\python3.10\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\soft\\python3.10\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\soft\\python3.10\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\soft\\python3.10\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\soft\\python3.10\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\soft\\python3.10\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\soft\\python3.10\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\soft\\python3.10\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\soft\\python3.10\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\soft\\python3.10\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\soft\\python3.10\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\soft\\python3.10\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\soft\\python3.10\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\soft\\python3.10\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\soft\\python3.10\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\soft\\python3.10\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\soft\\python3.10\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\soft\\python3.10\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\soft\\python3.10\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\soft\\python3.10\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\soft\\python3.10\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\soft\\python3.10\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\soft\\python3.10\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\soft\\python3.10\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\soft\\python3.10\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\soft\\python3.10\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\soft\\python3.10\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\soft\\python3.10\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\soft\\python3.10\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\soft\\python3.10\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\soft\\python3.10\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\soft\\python3.10\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\soft\\python3.10\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\soft\\python3.10\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\soft\\python3.10\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\soft\\python3.10\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\soft\\python3.10\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\soft\\python3.10\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\soft\\python3.10\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\soft\\python3.10\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('enum', 'D:\\soft\\python3.10\\lib\\enum.py', 'PYMODULE'),
  ('abc', 'D:\\soft\\python3.10\\lib\\abc.py', 'PYMODULE'),
  ('heapq', 'D:\\soft\\python3.10\\lib\\heapq.py', 'PYMODULE'),
  ('sre_constants', 'D:\\soft\\python3.10\\lib\\sre_constants.py', 'PYMODULE'),
  ('posixpath', 'D:\\soft\\python3.10\\lib\\posixpath.py', 'PYMODULE'),
  ('reprlib', 'D:\\soft\\python3.10\\lib\\reprlib.py', 'PYMODULE'),
  ('sre_compile', 'D:\\soft\\python3.10\\lib\\sre_compile.py', 'PYMODULE'),
  ('sre_parse', 'D:\\soft\\python3.10\\lib\\sre_parse.py', 'PYMODULE'),
  ('types', 'D:\\soft\\python3.10\\lib\\types.py', 'PYMODULE'),
  ('warnings', 'D:\\soft\\python3.10\\lib\\warnings.py', 'PYMODULE'),
  ('functools', 'D:\\soft\\python3.10\\lib\\functools.py', 'PYMODULE'),
  ('ntpath', 'D:\\soft\\python3.10\\lib\\ntpath.py', 'PYMODULE'),
  ('copyreg', 'D:\\soft\\python3.10\\lib\\copyreg.py', 'PYMODULE'),
  ('linecache', 'D:\\soft\\python3.10\\lib\\linecache.py', 'PYMODULE'),
  ('stat', 'D:\\soft\\python3.10\\lib\\stat.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\soft\\python3.10\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('weakref', 'D:\\soft\\python3.10\\lib\\weakref.py', 'PYMODULE'),
  ('os', 'D:\\soft\\python3.10\\lib\\os.py', 'PYMODULE')])
