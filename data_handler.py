#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
"""

import pandas as pd
from itertools import combinations
from datetime import datetime
import os
import csv
import json

class DataHandler:
    """数据处理类"""
    
    def __init__(self):
        self.products = []  # 商品列表
        self.orders = []    # 当前订单列表
        self.order_counter = 1  # 订单计数器
        self.history_file = "order_history.csv"  # 历史订单文件
        self.product_history_file = "product_history.json"  # 商品历史文件
        self._init_history_file()  # 初始化历史文件
        self._load_product_history()  # 加载商品历史
        
    def load_from_file(self, file_path):
        """从文件加载商品信息"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            else:
                df = pd.read_excel(file_path)
            
            # 检查必要的列
            required_columns = ['序号', '单价']
            for col in required_columns:
                if col not in df.columns:
                    # 尝试其他可能的列名
                    possible_names = {
                        '序号': ['商品序号', '序号', 'ID', 'id', '编号'],
                        '单价': ['单价', '价格', 'price', 'Price', '金额']
                    }
                    found = False
                    for possible_name in possible_names[col]:
                        if possible_name in df.columns:
                            df = df.rename(columns={possible_name: col})
                            found = True
                            break
                    if not found:
                        raise ValueError(f"文件中缺少必要的列：{col}")
            
            # 转换数据
            self.products = []
            for _, row in df.iterrows():
                product = {
                    "序号": int(row['序号']),
                    "单价": float(row['单价'])
                }
                self.products.append(product)
                
            # 按序号排序
            self.products.sort(key=lambda x: x['序号'])

            # 自动保存到历史
            self._save_product_history()

        except Exception as e:
            raise Exception(f"文件加载失败：{str(e)}")
    
    def load_from_data(self, data):
        """从数据列表加载商品信息"""
        self.products = data.copy()
        self.products.sort(key=lambda x: x['序号'])
        # 自动保存到历史
        self._save_product_history()
    
    def get_grouped_products(self, group_type):
        """获取分组后的商品
        
        Args:
            group_type (int): 分组类型，0=单双号分组，1=间隔分组
            
        Returns:
            dict: 分组后的商品字典
        """
        if not self.products:
            return {}
        
        grouped = {}
        
        if group_type == 0:  # 单双号分组
            odd_products = [p for p in self.products if p['序号'] % 2 == 1]
            even_products = [p for p in self.products if p['序号'] % 2 == 0]
            
            if odd_products:
                grouped['单号组'] = odd_products
            if even_products:
                grouped['双号组'] = even_products
                
        elif group_type == 1:  # 间隔分组（步长2）
            # 按序号模3分组：1,4,7... / 2,5,8... / 3,6,9...
            group_dict = {}
            for product in self.products:
                remainder = (product['序号'] - 1) % 3 + 1  # 计算余数，1,2,3循环
                group_name = f"组{remainder}"
                if group_name not in group_dict:
                    group_dict[group_name] = []
                group_dict[group_name].append(product)
            
            grouped = group_dict
        
        return grouped
    
    def generate_combinations(self, products, max_combo_size=4):
        """生成商品组合
        
        Args:
            products (list): 商品列表
            max_combo_size (int): 最大组合大小
            
        Returns:
            list: 组合列表
        """
        combinations_list = []
        
        # 单个商品
        for product in products:
            combo = {
                'products': [product],
                'text': str(product['序号']),
                'total_price': product['单价']
            }
            combinations_list.append(combo)
        
        # 多个商品组合（2到max_combo_size个）
        for size in range(2, min(len(products) + 1, max_combo_size + 1)):
            for combo_products in combinations(products, size):
                combo = {
                    'products': list(combo_products),
                    'text': '+'.join([str(p['序号']) for p in combo_products]),
                    'total_price': sum([p['单价'] for p in combo_products])
                }
                combinations_list.append(combo)
        
        return combinations_list
    
    def create_order(self, combo_data, quantity):
        """创建订单
        
        Args:
            combo_data (dict): 组合数据
            quantity (int): 数量
            
        Returns:
            dict: 订单信息
        """
        order = {
            "订单编号": f"ORD{self.order_counter:04d}",
            "商品序号组合": combo_data['text'],
            "商品单价": combo_data['total_price'],
            "数量": quantity,
            "单个商品金额": combo_data['total_price'],
            "订单总金额": combo_data['total_price'] * quantity,
            "创建时间": datetime.now()
        }
        
        self.orders.append(order)
        self.order_counter += 1
        
        return order
    
    def clear_orders(self):
        """清空订单列表"""
        self.orders = []
        self.order_counter = 1
    
    def get_orders(self):
        """获取所有订单"""
        return self.orders.copy()

    def get_classified_products(self, classification_type):
        """根据分类类型获取商品

        Args:
            classification_type (str): 分类类型
                - "odd": 奇数分类
                - "even": 偶数分类
                - "interval_1": 间隔2（1、4、7……）
                - "interval_2": 间隔2（2、5、8……）
                - "interval_3": 间隔2（3、6、9……）

        Returns:
            list: 分类后的商品列表
        """
        if not self.products:
            return []

        filtered_products = []

        if classification_type == "odd":
            # 奇数分类：序号为奇数的商品
            filtered_products = [p for p in self.products if p['序号'] % 2 == 1]

        elif classification_type == "even":
            # 偶数分类：序号为偶数的商品
            filtered_products = [p for p in self.products if p['序号'] % 2 == 0]

        elif classification_type == "interval_1":
            # 间隔2（1、4、7……）：序号 ≡ 1 (mod 3)
            filtered_products = [p for p in self.products if p['序号'] % 3 == 1]

        elif classification_type == "interval_2":
            # 间隔2（2、5、8……）：序号 ≡ 2 (mod 3)
            filtered_products = [p for p in self.products if p['序号'] % 3 == 2]

        elif classification_type == "interval_3":
            # 间隔2（3、6、9……）：序号 ≡ 0 (mod 3)
            filtered_products = [p for p in self.products if p['序号'] % 3 == 0]

        # 按序号排序
        filtered_products.sort(key=lambda x: x['序号'])
        return filtered_products

    def generate_order_combinations(self, products, combo_size, default_quantity):
        """生成订单组合（自动处理剩余商品）

        Args:
            products (list): 商品列表
            combo_size (int): 组合大小（2或3）
            default_quantity (int): 默认数量

        Returns:
            list: 订单组合列表
        """
        if not products:
            return []

        combinations_list = []

        # 按序号排序
        sorted_products = sorted(products, key=lambda x: x['序号'])

        # 生成指定大小的组合
        i = 0
        while i < len(sorted_products):
            if i + combo_size <= len(sorted_products):
                # 创建指定大小的组合
                combo_products = sorted_products[i:i + combo_size]
                combination = {
                    'products': combo_products,
                    'product_ids': '+'.join([str(p['序号']) for p in combo_products]),
                    'total_price': sum([p['单价'] for p in combo_products]),
                    'quantity': default_quantity,
                    'is_remainder': False
                }
                combinations_list.append(combination)
                i += combo_size
            else:
                # 处理剩余商品（单独成组）
                remaining_products = sorted_products[i:]
                if remaining_products:
                    combination = {
                        'products': remaining_products,
                        'product_ids': '+'.join([str(p['序号']) for p in remaining_products]),
                        'total_price': sum([p['单价'] for p in remaining_products]),
                        'quantity': default_quantity,
                        'is_remainder': True
                    }
                    combinations_list.append(combination)
                break

        return combinations_list

    def create_order_from_combination(self, combination):
        """从组合创建订单

        Args:
            combination (dict): 组合信息

        Returns:
            dict: 订单信息
        """
        order = {
            "订单编号": f"ORD{self.order_counter:04d}",
            "商品序号组合": combination['product_ids'],
            "组合单价": combination['total_price'],
            "数量": combination['quantity'],
            "单个商品金额": combination['total_price'],
            "订单总金额": combination['total_price'] * combination['quantity'],
            "创建时间": datetime.now(),
            "是否剩余商品": combination.get('is_remainder', False)
        }

        self.orders.append(order)
        self.order_counter += 1

        return order



    def get_order_statistics(self):
        """获取订单统计信息

        Returns:
            dict: 统计信息
        """
        if not self.orders:
            return {
                'total_orders': 0,
                'total_amount': 0.0,
                'remainder_orders': 0,
                'normal_orders': 0
            }

        total_orders = len(self.orders)
        total_amount = sum([order['订单总金额'] for order in self.orders])
        remainder_orders = sum([1 for order in self.orders if order.get('是否剩余商品', False)])
        normal_orders = total_orders - remainder_orders

        return {
            'total_orders': total_orders,
            'total_amount': total_amount,
            'remainder_orders': remainder_orders,
            'normal_orders': normal_orders
        }

    def _init_history_file(self):
        """初始化历史订单文件"""
        if not os.path.exists(self.history_file):
            # 创建CSV文件并写入表头
            headers = [
                '订单编号', '商品序号组合', '组合单价', '数量',
                '单个商品金额', '订单总金额', '创建时间'
            ]
            try:
                with open(self.history_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(headers)
            except Exception as e:
                print(f"初始化历史文件失败: {e}")

    def save_orders_to_history(self, orders, append_to_history=True):
        """保存订单到历史记录（按行追加，不做汇总）

        Args:
            orders (list): 订单列表
            append_to_history (bool): 是否追加到历史记录（True=追加，False=覆盖）
        """
        if not orders:
            return

        try:
            # 根据参数决定是追加还是覆盖
            if not append_to_history:
                print(f"正在清空历史订单文件: {self.history_file}")
                self.clear_history_orders()
                print("历史订单已清空，重新初始化文件")
                # 重新初始化文件（写入表头）
                self._init_history_file()
            else:
                print(f"正在追加订单到历史记录: {self.history_file}")
                # 确保历史文件存在
                if not os.path.exists(self.history_file):
                    self._init_history_file()

            # 按行追加订单数据，不做任何汇总
            with open(self.history_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for order in orders:
                    # 每个订单作为一行追加，保持原始数据
                    row = [
                        order.get('订单编号', ''),
                        order.get('商品序号组合', ''),
                        order.get('组合单价', 0),
                        order.get('数量', 0),
                        order.get('单个商品金额', 0),
                        order.get('订单总金额', 0),
                        order.get('创建时间', datetime.now()).strftime('%Y-%m-%d %H:%M:%S') if isinstance(order.get('创建时间'), datetime) else str(order.get('创建时间', ''))
                    ]
                    writer.writerow(row)

            action = "追加" if append_to_history else "覆盖保存"
            print(f"已{action} {len(orders)} 个订单到历史记录")

        except Exception as e:
            raise Exception(f"保存历史订单失败: {e}")

    def get_all_history_orders(self):
        """获取所有历史订单

        Returns:
            list: 历史订单列表
        """
        if not os.path.exists(self.history_file):
            return []

        try:
            history_orders = []
            with open(self.history_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 转换数据类型
                    order = {
                        '订单编号': row['订单编号'],
                        '商品序号组合': row['商品序号组合'],
                        '组合单价': float(row['组合单价']) if row['组合单价'] else 0.0,
                        '数量': int(row['数量']) if row['数量'] else 0,
                        '单个商品金额': float(row['单个商品金额']) if row['单个商品金额'] else 0.0,
                        '订单总金额': float(row['订单总金额']) if row['订单总金额'] else 0.0,
                        '创建时间': row['创建时间']
                    }
                    history_orders.append(order)
            return history_orders
        except Exception as e:
            raise Exception(f"读取历史订单失败: {e}")

    def clear_history_orders(self):
        """清空历史订单（同时清空文件和内存）"""
        try:
            # 清空内存中的历史订单列表（如果有的话）
            if hasattr(self, 'history_orders'):
                self.history_orders = []

            # 删除历史文件（若存在）
            if os.path.exists(self.history_file):
                os.remove(self.history_file)
                print(f"已删除历史文件: {self.history_file}")

            # 重新创建空的历史文件
            self._create_empty_history_table()

        except Exception as e:
            raise Exception(f"清空历史订单失败: {e}")

    def _create_empty_history_table(self):
        """创建空的历史表格文件"""
        try:
            headers = [
                '订单编号', '商品序号组合', '组合单价', '数量',
                '单个商品金额', '订单总金额', '创建时间'
            ]
            with open(self.history_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
            print(f"已创建空的历史表格: {self.history_file}")
        except Exception as e:
            print(f"创建空历史表格失败: {e}")

    def get_history_statistics(self):
        """获取历史订单统计信息

        Returns:
            dict: 历史统计信息
        """
        try:
            history_orders = self.get_all_history_orders()
            if not history_orders:
                return {
                    'total_orders': 0,
                    'total_amount': 0.0,
                    'date_range': '',
                    'remainder_orders': 0
                }

            total_orders = len(history_orders)
            total_amount = sum([order['订单总金额'] for order in history_orders])
            remainder_orders = sum([1 for order in history_orders if order.get('是否剩余商品', False)])

            # 获取日期范围
            dates = [order['创建时间'] for order in history_orders if order['创建时间']]
            date_range = f"{min(dates)} 至 {max(dates)}" if dates else "无数据"

            return {
                'total_orders': total_orders,
                'total_amount': total_amount,
                'date_range': date_range,
                'remainder_orders': remainder_orders
            }
        except Exception as e:
            return {
                'total_orders': 0,
                'total_amount': 0.0,
                'date_range': f'统计失败: {e}',
                'remainder_orders': 0
            }

    def _save_product_history(self):
        """保存商品信息到历史文件"""
        if not self.products:
            return

        try:
            product_data = {
                'products': self.products,
                'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_count': len(self.products)
            }
            with open(self.product_history_file, 'w', encoding='utf-8') as f:
                json.dump(product_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存商品历史失败: {e}")

    def _load_product_history(self):
        """加载历史商品信息"""
        if not os.path.exists(self.product_history_file):
            return

        try:
            with open(self.product_history_file, 'r', encoding='utf-8') as f:
                product_data = json.load(f)
                if 'products' in product_data and product_data['products']:
                    self.products = product_data['products']
                    print(f"已加载历史商品信息: {len(self.products)}个商品")
        except Exception as e:
            print(f"加载商品历史失败: {e}")

    def get_product_history_info(self):
        """获取商品历史信息"""
        if not os.path.exists(self.product_history_file):
            return None

        try:
            with open(self.product_history_file, 'r', encoding='utf-8') as f:
                product_data = json.load(f)
                return {
                    'save_time': product_data.get('save_time', '未知'),
                    'total_count': product_data.get('total_count', 0),
                    'has_data': len(product_data.get('products', [])) > 0
                }
        except Exception as e:
            return None

    def clear_product_history(self):
        """清空商品历史"""
        try:
            if os.path.exists(self.product_history_file):
                os.remove(self.product_history_file)
            self.products = []
        except Exception as e:
            raise Exception(f"清空商品历史失败: {e}")
