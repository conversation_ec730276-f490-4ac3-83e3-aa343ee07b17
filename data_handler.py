#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理模块
"""

import pandas as pd
from itertools import combinations
from datetime import datetime

class DataHandler:
    """数据处理类"""
    
    def __init__(self):
        self.products = []  # 商品列表
        self.orders = []    # 订单列表
        self.order_counter = 1  # 订单计数器
        
    def load_from_file(self, file_path):
        """从文件加载商品信息"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            else:
                df = pd.read_excel(file_path)
            
            # 检查必要的列
            required_columns = ['序号', '单价']
            for col in required_columns:
                if col not in df.columns:
                    # 尝试其他可能的列名
                    possible_names = {
                        '序号': ['商品序号', '序号', 'ID', 'id', '编号'],
                        '单价': ['单价', '价格', 'price', 'Price', '金额']
                    }
                    found = False
                    for possible_name in possible_names[col]:
                        if possible_name in df.columns:
                            df = df.rename(columns={possible_name: col})
                            found = True
                            break
                    if not found:
                        raise ValueError(f"文件中缺少必要的列：{col}")
            
            # 转换数据
            self.products = []
            for _, row in df.iterrows():
                product = {
                    "序号": int(row['序号']),
                    "单价": float(row['单价'])
                }
                self.products.append(product)
                
            # 按序号排序
            self.products.sort(key=lambda x: x['序号'])
            
        except Exception as e:
            raise Exception(f"文件加载失败：{str(e)}")
    
    def load_from_data(self, data):
        """从数据列表加载商品信息"""
        self.products = data.copy()
        self.products.sort(key=lambda x: x['序号'])
    
    def get_grouped_products(self, group_type):
        """获取分组后的商品
        
        Args:
            group_type (int): 分组类型，0=单双号分组，1=间隔分组
            
        Returns:
            dict: 分组后的商品字典
        """
        if not self.products:
            return {}
        
        grouped = {}
        
        if group_type == 0:  # 单双号分组
            odd_products = [p for p in self.products if p['序号'] % 2 == 1]
            even_products = [p for p in self.products if p['序号'] % 2 == 0]
            
            if odd_products:
                grouped['单号组'] = odd_products
            if even_products:
                grouped['双号组'] = even_products
                
        elif group_type == 1:  # 间隔分组（步长2）
            # 按序号模3分组：1,4,7... / 2,5,8... / 3,6,9...
            group_dict = {}
            for product in self.products:
                remainder = (product['序号'] - 1) % 3 + 1  # 计算余数，1,2,3循环
                group_name = f"组{remainder}"
                if group_name not in group_dict:
                    group_dict[group_name] = []
                group_dict[group_name].append(product)
            
            grouped = group_dict
        
        return grouped
    
    def generate_combinations(self, products, max_combo_size=4):
        """生成商品组合
        
        Args:
            products (list): 商品列表
            max_combo_size (int): 最大组合大小
            
        Returns:
            list: 组合列表
        """
        combinations_list = []
        
        # 单个商品
        for product in products:
            combo = {
                'products': [product],
                'text': str(product['序号']),
                'total_price': product['单价']
            }
            combinations_list.append(combo)
        
        # 多个商品组合（2到max_combo_size个）
        for size in range(2, min(len(products) + 1, max_combo_size + 1)):
            for combo_products in combinations(products, size):
                combo = {
                    'products': list(combo_products),
                    'text': '+'.join([str(p['序号']) for p in combo_products]),
                    'total_price': sum([p['单价'] for p in combo_products])
                }
                combinations_list.append(combo)
        
        return combinations_list
    
    def create_order(self, combo_data, quantity):
        """创建订单
        
        Args:
            combo_data (dict): 组合数据
            quantity (int): 数量
            
        Returns:
            dict: 订单信息
        """
        order = {
            "订单编号": f"ORD{self.order_counter:04d}",
            "商品序号组合": combo_data['text'],
            "商品单价": combo_data['total_price'],
            "数量": quantity,
            "单个商品金额": combo_data['total_price'],
            "订单总金额": combo_data['total_price'] * quantity,
            "创建时间": datetime.now()
        }
        
        self.orders.append(order)
        self.order_counter += 1
        
        return order
    
    def clear_orders(self):
        """清空订单列表"""
        self.orders = []
        self.order_counter = 1
    
    def get_orders(self):
        """获取所有订单"""
        return self.orders.copy()

    def get_classified_products(self, classification_type):
        """根据分类类型获取商品

        Args:
            classification_type (str): 分类类型
                - "odd": 奇数分类
                - "even": 偶数分类
                - "interval_1": 间隔2（1、4、7……）
                - "interval_2": 间隔2（2、5、8……）
                - "interval_3": 间隔2（3、6、9……）

        Returns:
            list: 分类后的商品列表
        """
        if not self.products:
            return []

        filtered_products = []

        if classification_type == "odd":
            # 奇数分类：序号为奇数的商品
            filtered_products = [p for p in self.products if p['序号'] % 2 == 1]

        elif classification_type == "even":
            # 偶数分类：序号为偶数的商品
            filtered_products = [p for p in self.products if p['序号'] % 2 == 0]

        elif classification_type == "interval_1":
            # 间隔2（1、4、7……）：序号 ≡ 1 (mod 3)
            filtered_products = [p for p in self.products if p['序号'] % 3 == 1]

        elif classification_type == "interval_2":
            # 间隔2（2、5、8……）：序号 ≡ 2 (mod 3)
            filtered_products = [p for p in self.products if p['序号'] % 3 == 2]

        elif classification_type == "interval_3":
            # 间隔2（3、6、9……）：序号 ≡ 0 (mod 3)
            filtered_products = [p for p in self.products if p['序号'] % 3 == 0]

        # 按序号排序
        filtered_products.sort(key=lambda x: x['序号'])
        return filtered_products

    def generate_order_combinations(self, products, combo_size, default_quantity):
        """生成订单组合（自动处理剩余商品）

        Args:
            products (list): 商品列表
            combo_size (int): 组合大小（2或3）
            default_quantity (int): 默认数量

        Returns:
            list: 订单组合列表
        """
        if not products:
            return []

        combinations_list = []

        # 按序号排序
        sorted_products = sorted(products, key=lambda x: x['序号'])

        # 生成指定大小的组合
        i = 0
        while i < len(sorted_products):
            if i + combo_size <= len(sorted_products):
                # 创建指定大小的组合
                combo_products = sorted_products[i:i + combo_size]
                combination = {
                    'products': combo_products,
                    'product_ids': '+'.join([str(p['序号']) for p in combo_products]),
                    'total_price': sum([p['单价'] for p in combo_products]),
                    'quantity': default_quantity,
                    'is_remainder': False
                }
                combinations_list.append(combination)
                i += combo_size
            else:
                # 处理剩余商品（单独成组）
                remaining_products = sorted_products[i:]
                if remaining_products:
                    combination = {
                        'products': remaining_products,
                        'product_ids': '+'.join([str(p['序号']) for p in remaining_products]),
                        'total_price': sum([p['单价'] for p in remaining_products]),
                        'quantity': default_quantity,
                        'is_remainder': True
                    }
                    combinations_list.append(combination)
                break

        return combinations_list

    def create_order_from_combination(self, combination):
        """从组合创建订单

        Args:
            combination (dict): 组合信息

        Returns:
            dict: 订单信息
        """
        order = {
            "订单编号": f"ORD{self.order_counter:04d}",
            "商品序号组合": combination['product_ids'],
            "组合单价": combination['total_price'],
            "数量": combination['quantity'],
            "单个商品金额": combination['total_price'],
            "订单总金额": combination['total_price'] * combination['quantity'],
            "创建时间": datetime.now(),
            "是否剩余商品": combination.get('is_remainder', False)
        }

        self.orders.append(order)
        self.order_counter += 1

        return order



    def get_order_statistics(self):
        """获取订单统计信息

        Returns:
            dict: 统计信息
        """
        if not self.orders:
            return {
                'total_orders': 0,
                'total_amount': 0.0,
                'remainder_orders': 0,
                'normal_orders': 0
            }

        total_orders = len(self.orders)
        total_amount = sum([order['订单总金额'] for order in self.orders])
        remainder_orders = sum([1 for order in self.orders if order.get('是否剩余商品', False)])
        normal_orders = total_orders - remainder_orders

        return {
            'total_orders': total_orders,
            'total_amount': total_amount,
            'remainder_orders': remainder_orders,
            'normal_orders': normal_orders
        }
