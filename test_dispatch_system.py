#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
派单系统测试文件
"""

import sys
from PyQt5.QtWidgets import QApplication
from ui_main import MainWindow

def test_dispatch_system():
    """测试派单系统功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    
    # 加载测试数据
    test_data = [
        {"序号": 1, "单价": 10.0},
        {"序号": 2, "单价": 15.0},
        {"序号": 3, "单价": 20.0},
        {"序号": 4, "单价": 25.0},
        {"序号": 5, "单价": 30.0},
        {"序号": 6, "单价": 35.0},
        {"序号": 7, "单价": 40.0},
        {"序号": 8, "单价": 45.0},
        {"序号": 9, "单价": 50.0},
        {"序号": 10, "单价": 55.0},
    ]
    
    window.data_handler.load_from_data(test_data)
    window.update_product_display()
    
    # 显示窗口
    window.show()
    
    print("派单系统测试启动成功！")
    print("功能测试说明（全面优化版）：")
    print("")
    print("=== 商品信息管理 ===")
    print("1. 商品历史自动保存：导入后自动保存，下次启动自动加载")
    print("2. 导入商品信息文件 或 使用示例数据")
    print("3. 清空商品历史（谨慎使用）")
    print("")
    print("=== 商品分类与组合 ===")
    print("4. 选择商品组合分类：")
    print("   - 奇数分类：筛选序号为奇数的商品（1、3、5...）")
    print("   - 偶数分类：筛选序号为偶数的商品（2、4、6...）")
    print("   - 间隔2（1、4、7……）：序号 ≡ 1 (mod 3)")
    print("   - 间隔2（2、5、8……）：序号 ≡ 2 (mod 3)")
    print("   - 间隔2（3、6、9……）：序号 ≡ 0 (mod 3)")
    print("5. 点击'生成商品池'查看分类结果")
    print("6. 选择组合类型（两两组合/三三组合）")
    print("7. 设置默认数量")
    print("")
    print("=== 订单生成与管理 ===")
    print("8. 点击'生成订单'（自动处理剩余商品）")
    print("9. 每次生成前自动清空历史订单")
    print("10. 查看订单列表和统计信息")
    print("")
    print("=== 导出功能 ===")
    print("11. 导出本次订单：仅导出当前显示的订单（无生成时间字段）")
    print("12. 导出历史订单：导出历史记录汇总（无创建时间字段，按批次分隔）")
    print("")
    print("=== 重要特性 ===")
    print("- 商品信息自动保存到 product_history.json")
    print("- 订单历史保存到 order_history.csv（每次生成前清空）")
    print("- 删除了冗余的分组选择区域")
    print("- 优化了表格格式，移除了冗余时间字段")
    print("- 只有在商品池已生成且组合类型已选择时，'生成订单'按钮才可用")
    print("")
    print("=== 新增功能（修复版）===")
    print("- ✅ 生成订单时自动清空历史订单（文件+内存）")
    print("- ✅ 添加操作提示音开关（系统设置区域）")
    print("- ✅ 历史派单汇总表按行追加，不做汇总计算")
    print("- ✅ 完善的声音反馈（成功/错误/点击提示音）")
    
    return app.exec_()

if __name__ == '__main__':
    sys.exit(test_dispatch_system())
