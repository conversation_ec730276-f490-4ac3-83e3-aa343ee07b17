#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
派单系统测试文件
"""

import sys
from PyQt5.QtWidgets import QApplication
from ui_main import MainWindow

def test_dispatch_system():
    """测试派单系统功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    
    # 加载测试数据
    test_data = [
        {"序号": 1, "单价": 10.0},
        {"序号": 2, "单价": 15.0},
        {"序号": 3, "单价": 20.0},
        {"序号": 4, "单价": 25.0},
        {"序号": 5, "单价": 30.0},
        {"序号": 6, "单价": 35.0},
        {"序号": 7, "单价": 40.0},
        {"序号": 8, "单价": 45.0},
        {"序号": 9, "单价": 50.0},
        {"序号": 10, "单价": 55.0},
    ]
    
    window.data_handler.load_from_data(test_data)
    window.update_product_display()
    
    # 显示窗口
    window.show()
    
    print("派单系统测试启动成功！")
    print("功能测试说明：")
    print("1. 选择商品组合分类（奇数/偶数/间隔2）")
    print("2. 点击'生成商品池'查看分类结果")
    print("3. 选择组合类型（两两组合/三三组合）")
    print("4. 点击'自动组合'或手动选择商品后点击'手动组合选中商品'")
    print("5. 在组合结果中设置数量")
    print("6. 点击'生成所有订单'创建订单")
    print("7. 查看订单列表和统计信息")
    print("8. 可导出Excel文件")
    
    return app.exec_()

if __name__ == '__main__':
    sys.exit(test_dispatch_system())
