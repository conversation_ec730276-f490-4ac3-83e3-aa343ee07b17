#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面UI模块
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QGridLayout, QLabel, QPushButton, QTableWidget,
                             QTableWidgetItem, QComboBox, QSpinBox, QRadioButton,
                             QButtonGroup, QFileDialog, QMessageBox, QGroupBox,
                             QHeaderView, QAbstractItemView, QListWidget, QListWidgetItem,
                             QSplitter, QScrollArea, QFrame, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from data_handler import DataHandler
from excel_handler import ExcelHandler

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.data_handler = DataHandler()
        self.excel_handler = ExcelHandler()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("桌面派单程序 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建各个功能区域
        self.create_data_import_area(main_layout)
        self.create_group_selection_area(main_layout)
        self.create_product_display_area(main_layout)
        self.create_order_combination_area(main_layout)
        self.create_order_list_area(main_layout)
        self.create_export_area(main_layout)
        
    def create_data_import_area(self, parent_layout):
        """创建数据导入区域"""
        group_box = QGroupBox("数据导入区")
        layout = QHBoxLayout(group_box)
        
        # 文件选择按钮
        self.file_select_btn = QPushButton("选择商品信息文件")
        self.file_select_btn.clicked.connect(self.select_file)
        layout.addWidget(self.file_select_btn)
        
        # 文件路径显示
        self.file_path_label = QLabel("未选择文件")
        layout.addWidget(self.file_path_label)
        
        # 手动输入按钮
        self.manual_input_btn = QPushButton("手动输入商品信息")
        self.manual_input_btn.clicked.connect(self.manual_input)
        layout.addWidget(self.manual_input_btn)
        
        layout.addStretch()
        parent_layout.addWidget(group_box)
        
    def create_group_selection_area(self, parent_layout):
        """创建分组选择区域"""
        group_box = QGroupBox("分组选择区")
        layout = QHBoxLayout(group_box)
        
        # 创建单选按钮组
        self.group_button_group = QButtonGroup()
        
        # 单双号分组
        self.odd_even_radio = QRadioButton("单双号分组")
        self.odd_even_radio.setChecked(True)
        self.group_button_group.addButton(self.odd_even_radio, 0)
        layout.addWidget(self.odd_even_radio)
        
        # 间隔分组
        self.interval_radio = QRadioButton("间隔分组(步长2)")
        self.group_button_group.addButton(self.interval_radio, 1)
        layout.addWidget(self.interval_radio)
        
        # 绑定事件
        self.group_button_group.buttonClicked.connect(self.on_group_changed)
        
        layout.addStretch()
        parent_layout.addWidget(group_box)
        
    def create_product_display_area(self, parent_layout):
        """创建商品展示区域"""
        group_box = QGroupBox("商品展示区")
        layout = QVBoxLayout(group_box)
        
        # 创建商品表格
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(3)
        self.product_table.setHorizontalHeaderLabels(["商品序号", "商品单价", "分组"])
        self.product_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.product_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        layout.addWidget(self.product_table)
        
        parent_layout.addWidget(group_box)
        
    def create_order_combination_area(self, parent_layout):
        """创建组合订单区域"""
        group_box = QGroupBox("组合订单区")
        main_layout = QVBoxLayout(group_box)

        # 第一行：商品组合分类选择
        classification_layout = QHBoxLayout()
        classification_layout.addWidget(QLabel("商品组合分类:"))

        self.classification_combo = QComboBox()
        self.classification_combo.addItem("按商品序号为奇数分类", "odd")
        self.classification_combo.addItem("按商品序号为偶数分类", "even")
        self.classification_combo.addItem("按序号间隔2分类", "interval")
        self.classification_combo.currentTextChanged.connect(self.on_classification_changed)
        classification_layout.addWidget(self.classification_combo)

        self.generate_pools_btn = QPushButton("生成商品池")
        self.generate_pools_btn.clicked.connect(self.generate_product_pools)
        classification_layout.addWidget(self.generate_pools_btn)

        classification_layout.addStretch()
        main_layout.addLayout(classification_layout)

        # 第二行：商品池展示区域
        pools_group = QGroupBox("商品池展示")
        pools_layout = QHBoxLayout(pools_group)

        # 创建商品池容器
        self.product_pools = {}
        self.pool_widgets = {}

        # 奇数池
        odd_frame = QFrame()
        odd_frame.setFrameStyle(QFrame.Box)
        odd_layout = QVBoxLayout(odd_frame)
        odd_layout.addWidget(QLabel("奇数池"))
        self.odd_pool_list = QListWidget()
        self.odd_pool_list.setSelectionMode(QListWidget.MultiSelection)
        odd_layout.addWidget(self.odd_pool_list)
        pools_layout.addWidget(odd_frame)
        self.pool_widgets['odd'] = self.odd_pool_list

        # 偶数池
        even_frame = QFrame()
        even_frame.setFrameStyle(QFrame.Box)
        even_layout = QVBoxLayout(even_frame)
        even_layout.addWidget(QLabel("偶数池"))
        self.even_pool_list = QListWidget()
        self.even_pool_list.setSelectionMode(QListWidget.MultiSelection)
        even_layout.addWidget(self.even_pool_list)
        pools_layout.addWidget(even_frame)
        self.pool_widgets['even'] = self.even_pool_list

        # 间隔池1
        interval1_frame = QFrame()
        interval1_frame.setFrameStyle(QFrame.Box)
        interval1_layout = QVBoxLayout(interval1_frame)
        interval1_layout.addWidget(QLabel("间隔2-池1"))
        self.interval1_pool_list = QListWidget()
        self.interval1_pool_list.setSelectionMode(QListWidget.MultiSelection)
        interval1_layout.addWidget(self.interval1_pool_list)
        pools_layout.addWidget(interval1_frame)
        self.pool_widgets['interval1'] = self.interval1_pool_list

        # 间隔池2
        interval2_frame = QFrame()
        interval2_frame.setFrameStyle(QFrame.Box)
        interval2_layout = QVBoxLayout(interval2_frame)
        interval2_layout.addWidget(QLabel("间隔2-池2"))
        self.interval2_pool_list = QListWidget()
        self.interval2_pool_list.setSelectionMode(QListWidget.MultiSelection)
        interval2_layout.addWidget(self.interval2_pool_list)
        pools_layout.addWidget(interval2_frame)
        self.pool_widgets['interval2'] = self.interval2_pool_list

        # 间隔池3
        interval3_frame = QFrame()
        interval3_frame.setFrameStyle(QFrame.Box)
        interval3_layout = QVBoxLayout(interval3_frame)
        interval3_layout.addWidget(QLabel("间隔2-池3"))
        self.interval3_pool_list = QListWidget()
        self.interval3_pool_list.setSelectionMode(QListWidget.MultiSelection)
        interval3_layout.addWidget(self.interval3_pool_list)
        pools_layout.addWidget(interval3_frame)
        self.pool_widgets['interval3'] = self.interval3_pool_list

        main_layout.addWidget(pools_group)

        # 第三行：订单组合选择功能
        combination_group = QGroupBox("订单组合选择")
        combination_layout = QGridLayout(combination_group)

        # 组合类型选择
        combination_layout.addWidget(QLabel("组合类型:"), 0, 0)
        self.combo_type_combo = QComboBox()
        self.combo_type_combo.addItem("两两组合", 2)
        self.combo_type_combo.addItem("三三组合", 3)
        combination_layout.addWidget(self.combo_type_combo, 0, 1)

        # 自动组合按钮
        self.auto_combine_btn = QPushButton("自动组合")
        self.auto_combine_btn.clicked.connect(self.auto_combine_products)
        combination_layout.addWidget(self.auto_combine_btn, 0, 2)

        # 手动组合按钮
        self.manual_combine_btn = QPushButton("手动组合选中商品")
        self.manual_combine_btn.clicked.connect(self.manual_combine_products)
        combination_layout.addWidget(self.manual_combine_btn, 0, 3)

        main_layout.addWidget(combination_group)

        # 第四行：组合结果展示和数量设置
        result_group = QGroupBox("组合结果与数量设置")
        result_layout = QVBoxLayout(result_group)

        self.combination_result_table = QTableWidget()
        self.combination_result_table.setColumnCount(4)
        self.combination_result_table.setHorizontalHeaderLabels([
            "组合序号", "商品序号组合", "组合单价", "数量"
        ])
        self.combination_result_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        result_layout.addWidget(self.combination_result_table)

        # 生成订单按钮
        generate_layout = QHBoxLayout()
        self.generate_orders_btn = QPushButton("生成所有订单")
        self.generate_orders_btn.clicked.connect(self.generate_all_orders)
        generate_layout.addWidget(self.generate_orders_btn)

        self.clear_combinations_btn = QPushButton("清空组合")
        self.clear_combinations_btn.clicked.connect(self.clear_combinations)
        generate_layout.addWidget(self.clear_combinations_btn)

        generate_layout.addStretch()
        result_layout.addLayout(generate_layout)

        main_layout.addWidget(result_group)

        parent_layout.addWidget(group_box)
        
    def create_order_list_area(self, parent_layout):
        """创建订单列表区域"""
        group_box = QGroupBox("订单列表区")
        layout = QVBoxLayout(group_box)

        # 创建订单表格
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(6)
        self.order_table.setHorizontalHeaderLabels([
            "订单编号", "商品序号组合", "组合单价", "数量", "单个商品金额", "订单总金额"
        ])
        self.order_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.order_table)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 清空订单按钮
        clear_btn = QPushButton("清空订单列表")
        clear_btn.clicked.connect(self.clear_orders)
        button_layout.addWidget(clear_btn)

        # 统计信息标签
        self.stats_label = QLabel("订单统计: 0个订单，总金额: ¥0.00")
        button_layout.addWidget(self.stats_label)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        parent_layout.addWidget(group_box)
        
    def create_export_area(self, parent_layout):
        """创建导出区域"""
        group_box = QGroupBox("导出区")
        layout = QHBoxLayout(group_box)
        
        # 导出Excel按钮
        self.export_btn = QPushButton("导出Excel")
        self.export_btn.clicked.connect(self.export_excel)
        layout.addWidget(self.export_btn)
        
        layout.addStretch()
        parent_layout.addWidget(group_box)
        
    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择商品信息文件", "", "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv)"
        )
        
        if file_path:
            self.file_path_label.setText(file_path)
            try:
                self.data_handler.load_from_file(file_path)
                self.update_product_display()
                QMessageBox.information(self, "成功", "商品信息加载成功！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"文件加载失败：{str(e)}")
                
    def manual_input(self):
        """手动输入商品信息"""
        # 这里可以打开一个对话框让用户手动输入
        # 为了简化，我们先添加一些示例数据
        sample_data = [
            {"序号": 1, "单价": 10.0},
            {"序号": 2, "单价": 15.0},
            {"序号": 3, "单价": 20.0},
            {"序号": 4, "单价": 25.0},
            {"序号": 5, "单价": 30.0},
            {"序号": 6, "单价": 35.0},
            {"序号": 7, "单价": 40.0},
            {"序号": 8, "单价": 45.0},
        ]
        
        self.data_handler.load_from_data(sample_data)
        self.update_product_display()
        QMessageBox.information(self, "成功", "示例商品信息已加载！")
        
    def on_group_changed(self):
        """分组方式改变"""
        self.update_product_display()
        
    def update_product_display(self):
        """更新商品显示"""
        if not self.data_handler.products:
            return
            
        # 获取当前分组方式
        group_type = self.group_button_group.checkedId()
        grouped_products = self.data_handler.get_grouped_products(group_type)
        
        # 更新商品表格
        all_products = []
        for group_name, products in grouped_products.items():
            for product in products:
                product_copy = product.copy()
                product_copy["分组"] = group_name
                all_products.append(product_copy)
        
        self.product_table.setRowCount(len(all_products))
        for i, product in enumerate(all_products):
            self.product_table.setItem(i, 0, QTableWidgetItem(str(product["序号"])))
            self.product_table.setItem(i, 1, QTableWidgetItem(f"{product['单价']:.2f}"))
            self.product_table.setItem(i, 2, QTableWidgetItem(product["分组"]))
        
        # 更新组合选择下拉框
        self.update_combination_combo(grouped_products)
        
    def update_combination_combo(self, grouped_products):
        """更新组合选择下拉框"""
        # 这个方法现在主要用于更新商品池
        self.update_product_pools(grouped_products)

    def update_product_pools(self, grouped_products):
        """更新商品池显示"""
        # 清空所有池
        for pool_widget in self.pool_widgets.values():
            pool_widget.clear()

        # 根据当前分类方式更新对应的池
        classification_type = self.classification_combo.currentData()

        if classification_type == "odd":
            # 奇数分类
            odd_products = [p for p in self.data_handler.products if p['序号'] % 2 == 1]
            for product in odd_products:
                item = QListWidgetItem(f"{product['序号']} (¥{product['单价']:.2f})")
                item.setData(Qt.UserRole, product)
                self.odd_pool_list.addItem(item)

        elif classification_type == "even":
            # 偶数分类
            even_products = [p for p in self.data_handler.products if p['序号'] % 2 == 0]
            for product in even_products:
                item = QListWidgetItem(f"{product['序号']} (¥{product['单价']:.2f})")
                item.setData(Qt.UserRole, product)
                self.even_pool_list.addItem(item)

        elif classification_type == "interval":
            # 间隔分类
            interval_groups = self.data_handler.get_interval_groups()
            pool_lists = [self.interval1_pool_list, self.interval2_pool_list, self.interval3_pool_list]

            for i, (group_name, products) in enumerate(interval_groups.items()):
                if i < len(pool_lists):
                    for product in products:
                        item = QListWidgetItem(f"{product['序号']} (¥{product['单价']:.2f})")
                        item.setData(Qt.UserRole, product)
                        pool_lists[i].addItem(item)
                
    def on_classification_changed(self):
        """分类方式改变时的处理"""
        if hasattr(self, 'data_handler') and self.data_handler.products:
            self.generate_product_pools()

    def generate_product_pools(self):
        """生成商品池"""
        if not self.data_handler.products:
            QMessageBox.warning(self, "警告", "请先加载商品信息！")
            return

        classification_type = self.classification_combo.currentData()

        # 清空所有池
        for pool_widget in self.pool_widgets.values():
            pool_widget.clear()

        if classification_type == "odd":
            # 奇数分类
            odd_products = [p for p in self.data_handler.products if p['序号'] % 2 == 1]
            for product in odd_products:
                item = QListWidgetItem(f"{product['序号']} (¥{product['单价']:.2f})")
                item.setData(Qt.UserRole, product)
                self.odd_pool_list.addItem(item)

        elif classification_type == "even":
            # 偶数分类
            even_products = [p for p in self.data_handler.products if p['序号'] % 2 == 0]
            for product in even_products:
                item = QListWidgetItem(f"{product['序号']} (¥{product['单价']:.2f})")
                item.setData(Qt.UserRole, product)
                self.even_pool_list.addItem(item)

        elif classification_type == "interval":
            # 间隔分类
            interval_groups = self.data_handler.get_interval_groups()
            pool_lists = [self.interval1_pool_list, self.interval2_pool_list, self.interval3_pool_list]

            for i, (group_name, products) in enumerate(interval_groups.items()):
                if i < len(pool_lists):
                    for product in products:
                        item = QListWidgetItem(f"{product['序号']} (¥{product['单价']:.2f})")
                        item.setData(Qt.UserRole, product)
                        pool_lists[i].addItem(item)

        QMessageBox.information(self, "成功", "商品池已生成！")

    def auto_combine_products(self):
        """自动组合商品"""
        classification_type = self.classification_combo.currentData()
        combo_size = self.combo_type_combo.currentData()

        # 获取当前活跃的商品池
        active_products = []
        if classification_type == "odd":
            active_products = self.get_products_from_pool(self.odd_pool_list)
        elif classification_type == "even":
            active_products = self.get_products_from_pool(self.even_pool_list)
        elif classification_type == "interval":
            # 对于间隔分类，从所有三个池中获取商品
            for pool_list in [self.interval1_pool_list, self.interval2_pool_list, self.interval3_pool_list]:
                active_products.extend(self.get_products_from_pool(pool_list))

        if not active_products:
            QMessageBox.warning(self, "警告", "没有可用的商品进行组合！")
            return

        # 生成自动组合
        combinations = self.data_handler.auto_generate_combinations(active_products, combo_size)
        self.display_combinations(combinations)

    def manual_combine_products(self):
        """手动组合选中的商品"""
        selected_products = []

        # 从所有池中获取选中的商品
        for pool_widget in self.pool_widgets.values():
            for item in pool_widget.selectedItems():
                product = item.data(Qt.UserRole)
                if product:
                    selected_products.append(product)

        if len(selected_products) < 2:
            QMessageBox.warning(self, "警告", "请至少选择2个商品进行组合！")
            return

        # 创建手动组合
        combination = self.data_handler.create_manual_combination(selected_products)
        self.display_combinations([combination])

        # 清除选择
        for pool_widget in self.pool_widgets.values():
            pool_widget.clearSelection()

    def get_products_from_pool(self, pool_widget):
        """从商品池获取所有商品"""
        products = []
        for i in range(pool_widget.count()):
            item = pool_widget.item(i)
            product = item.data(Qt.UserRole)
            if product:
                products.append(product)
        return products

    def display_combinations(self, combinations):
        """显示组合结果"""
        for combination in combinations:
            row_count = self.combination_result_table.rowCount()
            self.combination_result_table.insertRow(row_count)

            # 组合序号
            combo_id = f"C{row_count + 1:03d}"
            self.combination_result_table.setItem(row_count, 0, QTableWidgetItem(combo_id))

            # 商品序号组合
            product_ids = '+'.join([str(p['序号']) for p in combination['products']])
            self.combination_result_table.setItem(row_count, 1, QTableWidgetItem(product_ids))

            # 组合单价
            total_price = sum([p['单价'] for p in combination['products']])
            self.combination_result_table.setItem(row_count, 2, QTableWidgetItem(f"{total_price:.2f}"))

            # 数量输入框
            quantity_spinbox = QSpinBox()
            quantity_spinbox.setMinimum(1)
            quantity_spinbox.setMaximum(9999)
            quantity_spinbox.setValue(1)
            self.combination_result_table.setCellWidget(row_count, 3, quantity_spinbox)

    def clear_combinations(self):
        """清空组合结果"""
        self.combination_result_table.setRowCount(0)

    def generate_all_orders(self):
        """生成所有订单"""
        if self.combination_result_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有组合可生成订单！")
            return

        orders_generated = 0
        for row in range(self.combination_result_table.rowCount()):
            # 获取组合信息
            combo_id = self.combination_result_table.item(row, 0).text()
            product_ids = self.combination_result_table.item(row, 1).text()
            combo_price = float(self.combination_result_table.item(row, 2).text())

            # 获取数量
            quantity_widget = self.combination_result_table.cellWidget(row, 3)
            quantity = quantity_widget.value() if quantity_widget else 1

            # 创建订单数据
            combo_data = {
                'text': product_ids,
                'total_price': combo_price
            }

            # 生成订单
            order = self.data_handler.create_order(combo_data, quantity)

            # 添加到订单表格
            order_row = self.order_table.rowCount()
            self.order_table.insertRow(order_row)

            self.order_table.setItem(order_row, 0, QTableWidgetItem(order["订单编号"]))
            self.order_table.setItem(order_row, 1, QTableWidgetItem(order["商品序号组合"]))
            self.order_table.setItem(order_row, 2, QTableWidgetItem(f"{order['商品单价']:.2f}"))
            self.order_table.setItem(order_row, 3, QTableWidgetItem(str(order["数量"])))
            self.order_table.setItem(order_row, 4, QTableWidgetItem(f"{order['单个商品金额']:.2f}"))
            self.order_table.setItem(order_row, 5, QTableWidgetItem(f"{order['订单总金额']:.2f}"))

            orders_generated += 1

        QMessageBox.information(self, "成功", f"已生成 {orders_generated} 个订单！")
        self.clear_combinations()  # 清空组合结果
        self.update_order_stats()  # 更新统计信息
        
    def clear_orders(self):
        """清空订单列表"""
        self.order_table.setRowCount(0)
        self.data_handler.clear_orders()
        self.update_order_stats()

    def update_order_stats(self):
        """更新订单统计信息"""
        order_count = self.order_table.rowCount()
        total_amount = 0.0

        for row in range(order_count):
            amount_item = self.order_table.item(row, 5)  # 订单总金额列
            if amount_item:
                try:
                    total_amount += float(amount_item.text())
                except ValueError:
                    pass

        self.stats_label.setText(f"订单统计: {order_count}个订单，总金额: ¥{total_amount:.2f}")
        
    def export_excel(self):
        """导出Excel"""
        if self.order_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有订单数据可导出！")
            return
            
        # 获取保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", f"派单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel文件 (*.xlsx)"
        )
        
        if file_path:
            try:
                # 收集订单数据
                orders = []
                for row in range(self.order_table.rowCount()):
                    order = {
                        "订单编号": self.order_table.item(row, 0).text(),
                        "商品序号组合": self.order_table.item(row, 1).text(),
                        "商品单价": float(self.order_table.item(row, 2).text()),
                        "数量": int(self.order_table.item(row, 3).text()),
                        "单个商品金额": float(self.order_table.item(row, 4).text()),
                        "订单总金额": float(self.order_table.item(row, 5).text()),
                    }
                    orders.append(order)
                
                # 导出Excel
                self.excel_handler.export_orders(orders, file_path)
                QMessageBox.information(self, "成功", f"Excel文件已导出到：{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")
