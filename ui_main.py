#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面UI模块
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QGridLayout, QLabel, QPushButton, QTableWidget,
                             QTableWidgetItem, QComboBox, QSpinBox, QRadioButton,
                             QButtonGroup, QFileDialog, QMessageBox, QGroupBox,
                             QHeaderView, QAbstractItemView, QListWidget, QListWidgetItem,
                             QSplitter, QScrollArea, QFrame, QCheckBox)
from PyQt5.QtMultimedia import QSound
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from data_handler import DataHandler
from excel_handler import ExcelHandler

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.data_handler = DataHandler()
        self.excel_handler = ExcelHandler()

        # 声音控制设置
        self.sound_enabled = True  # 默认开启声音
        self.init_sounds()

        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("桌面派单程序 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建各个功能区域
        self.create_data_import_area(main_layout)
        self.create_product_display_area(main_layout)
        self.create_order_combination_area(main_layout)
        self.create_order_list_area(main_layout)
        self.create_export_area(main_layout)
        self.create_settings_area(main_layout)

        # 所有UI组件创建完成后，检查商品历史数据
        self.check_product_history()
        
    def create_data_import_area(self, parent_layout):
        """创建数据导入区域"""
        group_box = QGroupBox("商品信息管理")
        layout = QVBoxLayout(group_box)

        # 第一行：按钮区域
        button_layout = QHBoxLayout()

        # 文件选择按钮
        self.file_select_btn = QPushButton("导入商品信息文件")
        self.file_select_btn.clicked.connect(self.select_file)
        self.file_select_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(self.file_select_btn)

        # 手动输入按钮
        self.manual_input_btn = QPushButton("使用示例数据")
        self.manual_input_btn.clicked.connect(self.manual_input)
        self.manual_input_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        button_layout.addWidget(self.manual_input_btn)

        # 清空历史按钮
        self.clear_history_btn = QPushButton("清空商品历史")
        self.clear_history_btn.clicked.connect(self.clear_product_history)
        self.clear_history_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        button_layout.addWidget(self.clear_history_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 第二行：状态信息
        self.product_status_label = QLabel("正在检查商品历史...")
        self.product_status_label.setStyleSheet("color: #666; font-style: italic; padding: 5px;")
        layout.addWidget(self.product_status_label)

        parent_layout.addWidget(group_box)
        

        
    def create_product_display_area(self, parent_layout):
        """创建商品展示区域"""
        group_box = QGroupBox("商品展示区")
        layout = QVBoxLayout(group_box)

        # 商品统计信息
        self.product_info_label = QLabel("商品信息统计")
        self.product_info_label.setStyleSheet("font-weight: bold; color: #333; padding: 5px;")
        layout.addWidget(self.product_info_label)

        # 创建商品表格
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(2)
        self.product_table.setHorizontalHeaderLabels(["商品序号", "商品单价"])
        self.product_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.product_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.product_table.setAlternatingRowColors(True)
        layout.addWidget(self.product_table)

        parent_layout.addWidget(group_box)
        
    def create_order_combination_area(self, parent_layout):
        """创建组合订单区域"""
        group_box = QGroupBox("组合订单区")
        main_layout = QVBoxLayout(group_box)

        # 第一行：商品组合分类选择
        classification_layout = QHBoxLayout()
        classification_layout.addWidget(QLabel("商品组合分类:"))

        self.classification_combo = QComboBox()
        self.classification_combo.addItem("按商品序号为奇数分类", "odd")
        self.classification_combo.addItem("按商品序号为偶数分类", "even")
        self.classification_combo.addItem("间隔2（1、4、7……）", "interval_1")
        self.classification_combo.addItem("间隔2（2、5、8……）", "interval_2")
        self.classification_combo.addItem("间隔2（3、6、9……）", "interval_3")
        self.classification_combo.currentTextChanged.connect(self.on_classification_changed)
        classification_layout.addWidget(self.classification_combo)

        self.generate_pools_btn = QPushButton("生成商品池")
        self.generate_pools_btn.clicked.connect(self.generate_product_pools)
        classification_layout.addWidget(self.generate_pools_btn)

        classification_layout.addStretch()
        main_layout.addLayout(classification_layout)

        # 第二行：商品池展示区域
        pools_group = QGroupBox("商品池展示")
        pools_layout = QVBoxLayout(pools_group)

        # 商品池状态标签
        self.pool_status_label = QLabel("请先选择分类规则并生成商品池")
        self.pool_status_label.setStyleSheet("color: #666; font-style: italic;")
        pools_layout.addWidget(self.pool_status_label)

        # 统一的商品池展示
        self.current_pool_list = QListWidget()
        self.current_pool_list.setSelectionMode(QListWidget.MultiSelection)
        pools_layout.addWidget(self.current_pool_list)

        main_layout.addWidget(pools_group)

        # 商品池生成状态标记
        self.pool_generated = False
        self.current_classification = None

        # 第三行：订单生成控制
        order_control_group = QGroupBox("订单生成控制")
        order_control_layout = QHBoxLayout(order_control_group)

        # 组合类型选择
        order_control_layout.addWidget(QLabel("组合类型:"))
        self.combo_type_combo = QComboBox()
        self.combo_type_combo.addItem("两两组合", 2)
        self.combo_type_combo.addItem("三三组合", 3)
        self.combo_type_combo.currentTextChanged.connect(self.on_combo_type_changed)
        order_control_layout.addWidget(self.combo_type_combo)

        # 数量设置
        order_control_layout.addWidget(QLabel("默认数量:"))
        self.default_quantity_spinbox = QSpinBox()
        self.default_quantity_spinbox.setMinimum(1)
        self.default_quantity_spinbox.setMaximum(9999)
        self.default_quantity_spinbox.setValue(1)
        order_control_layout.addWidget(self.default_quantity_spinbox)

        # 生成订单按钮
        self.generate_orders_btn = QPushButton("生成订单")
        self.generate_orders_btn.clicked.connect(self.generate_orders_from_pool)
        self.generate_orders_btn.setEnabled(False)  # 初始状态禁用
        self.generate_orders_btn.setStyleSheet("""
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QPushButton:enabled {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
            }
        """)
        order_control_layout.addWidget(self.generate_orders_btn)

        order_control_layout.addStretch()
        main_layout.addWidget(order_control_group)

        parent_layout.addWidget(group_box)
        
    def create_order_list_area(self, parent_layout):
        """创建订单列表区域"""
        group_box = QGroupBox("订单列表区")
        layout = QVBoxLayout(group_box)

        # 创建订单表格
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(6)
        self.order_table.setHorizontalHeaderLabels([
            "订单编号", "商品序号组合", "组合单价", "数量", "单个商品金额", "订单总金额"
        ])
        self.order_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.order_table)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 清空订单按钮
        clear_btn = QPushButton("清空订单列表")
        clear_btn.clicked.connect(self.clear_orders)
        button_layout.addWidget(clear_btn)

        # 统计信息标签
        self.stats_label = QLabel("订单统计: 0个订单，总金额: ¥0.00")
        button_layout.addWidget(self.stats_label)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        parent_layout.addWidget(group_box)
        
    def create_export_area(self, parent_layout):
        """创建导出区域"""
        group_box = QGroupBox("导出区")
        layout = QHBoxLayout(group_box)

        # 导出本次订单按钮
        self.export_current_btn = QPushButton("导出本次订单")
        self.export_current_btn.clicked.connect(self.export_current_orders)
        self.export_current_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        layout.addWidget(self.export_current_btn)

        # 导出历史订单按钮
        self.export_history_btn = QPushButton("导出历史订单")
        self.export_history_btn.clicked.connect(self.export_history_orders)
        self.export_history_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(self.export_history_btn)

        layout.addStretch()
        parent_layout.addWidget(group_box)

    def create_settings_area(self, parent_layout):
        """创建设置区域"""
        group_box = QGroupBox("系统设置")
        layout = QHBoxLayout(group_box)

        # 声音控制复选框
        self.sound_checkbox = QCheckBox("关闭操作提示音")
        self.sound_checkbox.setChecked(not self.sound_enabled)  # 默认不勾选（声音开启）
        self.sound_checkbox.toggled.connect(self.toggle_sound)
        self.sound_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #333;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #ccc;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #4CAF50;
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.sound_checkbox)

        # 声音状态显示
        self.sound_status_label = QLabel("🔊 提示音已开启")
        self.sound_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        layout.addWidget(self.sound_status_label)

        layout.addStretch()
        parent_layout.addWidget(group_box)

    def check_product_history(self):
        """检查商品历史数据"""
        # 安全检查：确保必要的UI组件已创建
        if not hasattr(self, 'product_status_label'):
            print("警告：product_status_label 未定义，跳过历史检查")
            return

        history_info = self.data_handler.get_product_history_info()
        if history_info and history_info['has_data']:
            self.product_status_label.setText(
                f"已加载历史商品信息 | 保存时间: {history_info['save_time']} | 商品数量: {history_info['total_count']}"
            )
            self.product_status_label.setStyleSheet("color: #4CAF50; font-weight: bold; padding: 5px;")
            # 自动更新商品显示
            self.update_product_display()
        else:
            self.product_status_label.setText("暂无商品历史数据，请导入商品信息")
            self.product_status_label.setStyleSheet("color: #666; font-style: italic; padding: 5px;")

    def clear_product_history(self):
        """清空商品历史"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有商品历史数据吗？此操作不可恢复。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.data_handler.clear_product_history()
                self.product_table.setRowCount(0)
                self.current_pool_list.clear()
                self.pool_generated = False
                self.current_classification = None
                self.update_generate_button_state()
                self.check_product_history()
                QMessageBox.information(self, "成功", "商品历史数据已清空！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"清空失败：{str(e)}")

    def init_sounds(self):
        """初始化声音资源"""
        try:
            # 创建简单的系统提示音（如果没有声音文件，使用系统默认声音）
            self.success_sound = None
            self.error_sound = None
            self.click_sound = None

            # 尝试使用系统默认声音
            try:
                # 这里可以添加自定义声音文件路径
                # self.success_sound = QSound("sounds/success.wav")
                # self.error_sound = QSound("sounds/error.wav")
                # self.click_sound = QSound("sounds/click.wav")
                pass
            except:
                pass

        except Exception as e:
            print(f"声音初始化失败: {e}")

    def toggle_sound(self, is_checked):
        """控制操作提示音开关"""
        self.sound_enabled = not is_checked

        if self.sound_enabled:
            self.sound_status_label.setText("🔊 提示音已开启")
            self.sound_status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            self.play_sound("success")  # 播放开启声音
        else:
            self.sound_status_label.setText("🔇 提示音已关闭")
            self.sound_status_label.setStyleSheet("color: #f44336; font-weight: bold;")

    def play_sound(self, sound_type="click"):
        """播放操作提示音"""
        if not self.sound_enabled:
            return

        try:
            # 使用系统默认声音
            if sound_type == "success":
                # 播放成功提示音
                import winsound
                winsound.MessageBeep(winsound.MB_OK)
            elif sound_type == "error":
                # 播放错误提示音
                import winsound
                winsound.MessageBeep(winsound.MB_ICONHAND)
            elif sound_type == "click":
                # 播放点击提示音
                import winsound
                winsound.MessageBeep(winsound.MB_ICONASTERISK)
        except Exception as e:
            # 如果声音播放失败，静默处理
            pass

    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择商品信息文件", "", "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv)"
        )

        if file_path:
            try:
                self.data_handler.load_from_file(file_path)
                self.update_product_display()
                self.check_product_history()  # 更新状态显示
                self.play_sound("success")  # 播放成功提示音
                QMessageBox.information(self, "成功", "商品信息导入并保存成功！")
            except Exception as e:
                self.play_sound("error")  # 播放错误提示音
                QMessageBox.critical(self, "错误", f"文件加载失败：{str(e)}")
                
    def manual_input(self):
        """手动输入商品信息"""
        # 这里可以打开一个对话框让用户手动输入
        # 为了简化，我们先添加一些示例数据
        sample_data = [
            {"序号": 1, "单价": 10.0},
            {"序号": 2, "单价": 15.0},
            {"序号": 3, "单价": 20.0},
            {"序号": 4, "单价": 25.0},
            {"序号": 5, "单价": 30.0},
            {"序号": 6, "单价": 35.0},
            {"序号": 7, "单价": 40.0},
            {"序号": 8, "单价": 45.0},
        ]
        
        self.data_handler.load_from_data(sample_data)
        self.update_product_display()
        self.check_product_history()  # 更新状态显示
        self.play_sound("success")  # 播放成功提示音
        QMessageBox.information(self, "成功", "示例商品信息已加载并保存！")
        

        
    def update_product_display(self):
        """更新商品显示"""
        # 安全检查：确保必要的UI组件已创建
        if not hasattr(self, 'product_info_label'):
            print("警告：product_info_label 未定义，跳过商品显示更新")
            return

        if not hasattr(self, 'product_table'):
            print("警告：product_table 未定义，跳过商品显示更新")
            return

        if not self.data_handler.products:
            self.product_info_label.setText("暂无商品数据")
            return

        # 更新商品信息统计
        total_products = len(self.data_handler.products)
        total_value = sum(p['单价'] for p in self.data_handler.products)
        avg_price = total_value / total_products if total_products > 0 else 0

        self.product_info_label.setText(
            f"商品总数: {total_products} | 总价值: ¥{total_value:.2f} | 平均单价: ¥{avg_price:.2f}"
        )

        # 更新商品表格 - 显示所有商品
        self.product_table.setRowCount(len(self.data_handler.products))
        for i, product in enumerate(self.data_handler.products):
            self.product_table.setItem(i, 0, QTableWidgetItem(str(product["序号"])))
            self.product_table.setItem(i, 1, QTableWidgetItem(f"{product['单价']:.2f}"))
        
    def update_combination_combo(self, grouped_products):
        """更新组合选择下拉框（保留兼容性）"""
        # 这个方法现在主要用于保持兼容性，实际功能已转移到新的商品池生成逻辑
        pass
                
    def on_classification_changed(self):
        """分类方式改变时的处理"""
        # 重置商品池状态
        self.pool_generated = False
        self.current_classification = None
        self.current_pool_list.clear()
        self.pool_status_label.setText("请先选择分类规则并生成商品池")
        self.update_generate_button_state()

    def on_combo_type_changed(self):
        """组合类型改变时的处理"""
        self.update_generate_button_state()

    def generate_product_pools(self):
        """生成商品池"""
        if not self.data_handler.products:
            QMessageBox.warning(self, "警告", "请先加载商品信息！")
            return

        classification_type = self.classification_combo.currentData()

        # 清空当前商品池
        self.current_pool_list.clear()

        # 根据分类类型获取商品
        filtered_products = self.data_handler.get_classified_products(classification_type)

        if not filtered_products:
            QMessageBox.warning(self, "警告", "当前分类规则下没有匹配的商品！")
            return

        # 填充商品池
        for product in filtered_products:
            item = QListWidgetItem(f"序号{product['序号']} (¥{product['单价']:.2f})")
            item.setData(Qt.UserRole, product)
            self.current_pool_list.addItem(item)

        # 更新状态
        self.pool_generated = True
        self.current_classification = classification_type

        # 更新状态标签
        classification_names = {
            "odd": "奇数分类",
            "even": "偶数分类",
            "interval_1": "间隔2（1、4、7……）",
            "interval_2": "间隔2（2、5、8……）",
            "interval_3": "间隔2（3、6、9……）"
        }
        self.pool_status_label.setText(f"当前分类: {classification_names.get(classification_type, '未知')} | 商品数量: {len(filtered_products)}")

        # 更新生成订单按钮状态
        self.update_generate_button_state()

        QMessageBox.information(self, "成功", f"商品池已生成！共{len(filtered_products)}个商品。")

    def update_generate_button_state(self):
        """更新生成订单按钮状态"""
        # 检查两个条件：商品池已生成 且 组合类型已选择
        can_generate = (
            self.pool_generated and
            self.current_classification is not None and
            self.combo_type_combo.currentData() is not None
        )

        self.generate_orders_btn.setEnabled(can_generate)

        if can_generate:
            self.generate_orders_btn.setText("生成订单")
        else:
            if not self.pool_generated:
                self.generate_orders_btn.setText("生成订单 (请先生成商品池)")
            else:
                self.generate_orders_btn.setText("生成订单 (请选择组合类型)")

    def generate_orders_from_pool(self):
        """从商品池生成订单"""
        if not self.pool_generated or not self.current_classification:
            QMessageBox.warning(self, "警告", "请先生成商品池！")
            return

        combo_size = self.combo_type_combo.currentData()
        default_quantity = self.default_quantity_spinbox.value()

        # 获取当前商品池中的所有商品
        pool_products = []
        for i in range(self.current_pool_list.count()):
            item = self.current_pool_list.item(i)
            product = item.data(Qt.UserRole)
            if product:
                pool_products.append(product)

        if not pool_products:
            QMessageBox.warning(self, "警告", "商品池为空，无法生成订单！")
            return

        # 使用数据处理器生成订单组合
        order_combinations = self.data_handler.generate_order_combinations(
            pool_products, combo_size, default_quantity
        )

        if not order_combinations:
            QMessageBox.warning(self, "警告", "无法生成有效的订单组合！")
            return

        # 生成订单并添加到订单表格
        orders_generated = 0
        current_batch_orders = []  # 存储本次生成的订单

        for combination in order_combinations:
            order = self.data_handler.create_order_from_combination(combination)

            # 添加到订单表格
            row_count = self.order_table.rowCount()
            self.order_table.insertRow(row_count)

            self.order_table.setItem(row_count, 0, QTableWidgetItem(order["订单编号"]))
            self.order_table.setItem(row_count, 1, QTableWidgetItem(order["商品序号组合"]))
            self.order_table.setItem(row_count, 2, QTableWidgetItem(f"{order['组合单价']:.2f}"))
            self.order_table.setItem(row_count, 3, QTableWidgetItem(str(order["数量"])))
            self.order_table.setItem(row_count, 4, QTableWidgetItem(f"{order['单个商品金额']:.2f}"))
            self.order_table.setItem(row_count, 5, QTableWidgetItem(f"{order['订单总金额']:.2f}"))

            # 收集本次订单用于历史记录保存
            current_batch_orders.append(order)
            orders_generated += 1

        # 保存到历史记录（清空之前的历史）
        try:
            self.data_handler.save_orders_to_history(current_batch_orders, clear_before_save=True)
        except Exception as e:
            QMessageBox.warning(self, "警告", f"订单历史记录保存失败：{str(e)}")

        # 更新统计信息
        self.update_order_stats()

        self.play_sound("success")  # 播放成功提示音
        QMessageBox.information(self, "成功", f"已生成 {orders_generated} 个订单！\n包含剩余商品的自动处理。\n历史订单已清空并保存新订单。")




        
    def clear_orders(self):
        """清空订单列表"""
        self.order_table.setRowCount(0)
        self.data_handler.clear_orders()
        self.update_order_stats()

    def update_order_stats(self):
        """更新订单统计信息"""
        order_count = self.order_table.rowCount()
        total_amount = 0.0

        for row in range(order_count):
            amount_item = self.order_table.item(row, 5)  # 订单总金额列
            if amount_item:
                try:
                    total_amount += float(amount_item.text())
                except ValueError:
                    pass

        self.stats_label.setText(f"订单统计: {order_count}个订单，总金额: ¥{total_amount:.2f}")
        
    def export_current_orders(self):
        """导出本次订单"""
        if self.order_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有本次订单数据可导出！")
            return

        # 获取保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存本次订单Excel文件", f"本次派单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel文件 (*.xlsx)"
        )

        if file_path:
            try:
                # 收集本次订单数据
                current_orders = []
                for row in range(self.order_table.rowCount()):
                    order = {
                        "订单编号": self.order_table.item(row, 0).text(),
                        "商品序号组合": self.order_table.item(row, 1).text(),
                        "组合单价": float(self.order_table.item(row, 2).text()),
                        "数量": int(self.order_table.item(row, 3).text()),
                        "单个商品金额": float(self.order_table.item(row, 4).text()),
                        "订单总金额": float(self.order_table.item(row, 5).text())
                    }
                    current_orders.append(order)

                # 导出Excel
                self.excel_handler.export_current_orders(current_orders, file_path)
                self.play_sound("success")  # 播放成功提示音
                QMessageBox.information(self, "成功", f"本次订单Excel文件已导出到：{file_path}")

            except Exception as e:
                self.play_sound("error")  # 播放错误提示音
                QMessageBox.critical(self, "错误", f"导出本次订单失败：{str(e)}")

    def export_history_orders(self):
        """导出历史订单"""
        try:
            # 从数据处理器获取历史订单
            history_orders = self.data_handler.get_all_history_orders()

            if not history_orders:
                QMessageBox.warning(self, "警告", "没有历史订单数据可导出！")
                return

            # 获取保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存历史订单Excel文件", f"历史派单汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel文件 (*.xlsx)"
            )

            if file_path:
                # 导出历史订单Excel
                self.excel_handler.export_history_orders(history_orders, file_path)
                self.play_sound("success")  # 播放成功提示音
                QMessageBox.information(self, "成功", f"历史订单Excel文件已导出到：{file_path}\n共导出 {len(history_orders)} 条历史记录")

        except Exception as e:
            self.play_sound("error")  # 播放错误提示音
            QMessageBox.critical(self, "错误", f"导出历史订单失败：{str(e)}")
