#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面UI模块
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QLabel, QPushButton, QTableWidget, 
                             QTableWidgetItem, QComboBox, QSpinBox, QRadioButton,
                             QButtonGroup, QFileDialog, QMessageBox, QGroupBox,
                             QHeaderView, QAbstractItemView)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

from data_handler import DataHandler
from excel_handler import ExcelHandler

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.data_handler = DataHandler()
        self.excel_handler = ExcelHandler()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("桌面派单程序 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建各个功能区域
        self.create_data_import_area(main_layout)
        self.create_group_selection_area(main_layout)
        self.create_product_display_area(main_layout)
        self.create_order_combination_area(main_layout)
        self.create_order_list_area(main_layout)
        self.create_export_area(main_layout)
        
    def create_data_import_area(self, parent_layout):
        """创建数据导入区域"""
        group_box = QGroupBox("数据导入区")
        layout = QHBoxLayout(group_box)
        
        # 文件选择按钮
        self.file_select_btn = QPushButton("选择商品信息文件")
        self.file_select_btn.clicked.connect(self.select_file)
        layout.addWidget(self.file_select_btn)
        
        # 文件路径显示
        self.file_path_label = QLabel("未选择文件")
        layout.addWidget(self.file_path_label)
        
        # 手动输入按钮
        self.manual_input_btn = QPushButton("手动输入商品信息")
        self.manual_input_btn.clicked.connect(self.manual_input)
        layout.addWidget(self.manual_input_btn)
        
        layout.addStretch()
        parent_layout.addWidget(group_box)
        
    def create_group_selection_area(self, parent_layout):
        """创建分组选择区域"""
        group_box = QGroupBox("分组选择区")
        layout = QHBoxLayout(group_box)
        
        # 创建单选按钮组
        self.group_button_group = QButtonGroup()
        
        # 单双号分组
        self.odd_even_radio = QRadioButton("单双号分组")
        self.odd_even_radio.setChecked(True)
        self.group_button_group.addButton(self.odd_even_radio, 0)
        layout.addWidget(self.odd_even_radio)
        
        # 间隔分组
        self.interval_radio = QRadioButton("间隔分组(步长2)")
        self.group_button_group.addButton(self.interval_radio, 1)
        layout.addWidget(self.interval_radio)
        
        # 绑定事件
        self.group_button_group.buttonClicked.connect(self.on_group_changed)
        
        layout.addStretch()
        parent_layout.addWidget(group_box)
        
    def create_product_display_area(self, parent_layout):
        """创建商品展示区域"""
        group_box = QGroupBox("商品展示区")
        layout = QVBoxLayout(group_box)
        
        # 创建商品表格
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(3)
        self.product_table.setHorizontalHeaderLabels(["商品序号", "商品单价", "分组"])
        self.product_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.product_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        layout.addWidget(self.product_table)
        
        parent_layout.addWidget(group_box)
        
    def create_order_combination_area(self, parent_layout):
        """创建组合订单区域"""
        group_box = QGroupBox("组合订单区")
        layout = QGridLayout(group_box)
        
        # 商品组合选择
        layout.addWidget(QLabel("选择商品组合:"), 0, 0)
        self.combination_combo = QComboBox()
        layout.addWidget(self.combination_combo, 0, 1)
        
        # 商品数量
        layout.addWidget(QLabel("商品数量:"), 0, 2)
        self.quantity_spinbox = QSpinBox()
        self.quantity_spinbox.setMinimum(1)
        self.quantity_spinbox.setMaximum(9999)
        self.quantity_spinbox.setValue(1)
        layout.addWidget(self.quantity_spinbox, 0, 3)
        
        # 计算按钮
        self.calculate_btn = QPushButton("生成订单")
        self.calculate_btn.clicked.connect(self.generate_order)
        layout.addWidget(self.calculate_btn, 0, 4)
        
        parent_layout.addWidget(group_box)
        
    def create_order_list_area(self, parent_layout):
        """创建订单列表区域"""
        group_box = QGroupBox("订单列表区")
        layout = QVBoxLayout(group_box)
        
        # 创建订单表格
        self.order_table = QTableWidget()
        self.order_table.setColumnCount(6)
        self.order_table.setHorizontalHeaderLabels([
            "订单编号", "商品序号组合", "商品单价", "数量", "单个商品金额", "订单总金额"
        ])
        self.order_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        layout.addWidget(self.order_table)
        
        # 清空订单按钮
        clear_btn = QPushButton("清空订单列表")
        clear_btn.clicked.connect(self.clear_orders)
        layout.addWidget(clear_btn)
        
        parent_layout.addWidget(group_box)
        
    def create_export_area(self, parent_layout):
        """创建导出区域"""
        group_box = QGroupBox("导出区")
        layout = QHBoxLayout(group_box)
        
        # 导出Excel按钮
        self.export_btn = QPushButton("导出Excel")
        self.export_btn.clicked.connect(self.export_excel)
        layout.addWidget(self.export_btn)
        
        layout.addStretch()
        parent_layout.addWidget(group_box)
        
    def select_file(self):
        """选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择商品信息文件", "", "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv)"
        )
        
        if file_path:
            self.file_path_label.setText(file_path)
            try:
                self.data_handler.load_from_file(file_path)
                self.update_product_display()
                QMessageBox.information(self, "成功", "商品信息加载成功！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"文件加载失败：{str(e)}")
                
    def manual_input(self):
        """手动输入商品信息"""
        # 这里可以打开一个对话框让用户手动输入
        # 为了简化，我们先添加一些示例数据
        sample_data = [
            {"序号": 1, "单价": 10.0},
            {"序号": 2, "单价": 15.0},
            {"序号": 3, "单价": 20.0},
            {"序号": 4, "单价": 25.0},
            {"序号": 5, "单价": 30.0},
            {"序号": 6, "单价": 35.0},
            {"序号": 7, "单价": 40.0},
            {"序号": 8, "单价": 45.0},
        ]
        
        self.data_handler.load_from_data(sample_data)
        self.update_product_display()
        QMessageBox.information(self, "成功", "示例商品信息已加载！")
        
    def on_group_changed(self):
        """分组方式改变"""
        self.update_product_display()
        
    def update_product_display(self):
        """更新商品显示"""
        if not self.data_handler.products:
            return
            
        # 获取当前分组方式
        group_type = self.group_button_group.checkedId()
        grouped_products = self.data_handler.get_grouped_products(group_type)
        
        # 更新商品表格
        all_products = []
        for group_name, products in grouped_products.items():
            for product in products:
                product_copy = product.copy()
                product_copy["分组"] = group_name
                all_products.append(product_copy)
        
        self.product_table.setRowCount(len(all_products))
        for i, product in enumerate(all_products):
            self.product_table.setItem(i, 0, QTableWidgetItem(str(product["序号"])))
            self.product_table.setItem(i, 1, QTableWidgetItem(f"{product['单价']:.2f}"))
            self.product_table.setItem(i, 2, QTableWidgetItem(product["分组"]))
        
        # 更新组合选择下拉框
        self.update_combination_combo(grouped_products)
        
    def update_combination_combo(self, grouped_products):
        """更新组合选择下拉框"""
        self.combination_combo.clear()
        
        for group_name, products in grouped_products.items():
            # 生成该组内的所有可能组合
            combinations = self.data_handler.generate_combinations(products)
            for combo in combinations:
                combo_text = f"{group_name}: {combo['text']}"
                self.combination_combo.addItem(combo_text, combo)
                
    def generate_order(self):
        """生成订单"""
        if self.combination_combo.currentData() is None:
            QMessageBox.warning(self, "警告", "请先选择商品组合！")
            return
            
        combo_data = self.combination_combo.currentData()
        quantity = self.quantity_spinbox.value()
        
        # 生成订单
        order = self.data_handler.create_order(combo_data, quantity)
        
        # 添加到订单表格
        row_count = self.order_table.rowCount()
        self.order_table.insertRow(row_count)
        
        self.order_table.setItem(row_count, 0, QTableWidgetItem(order["订单编号"]))
        self.order_table.setItem(row_count, 1, QTableWidgetItem(order["商品序号组合"]))
        self.order_table.setItem(row_count, 2, QTableWidgetItem(f"{order['商品单价']:.2f}"))
        self.order_table.setItem(row_count, 3, QTableWidgetItem(str(order["数量"])))
        self.order_table.setItem(row_count, 4, QTableWidgetItem(f"{order['单个商品金额']:.2f}"))
        self.order_table.setItem(row_count, 5, QTableWidgetItem(f"{order['订单总金额']:.2f}"))
        
    def clear_orders(self):
        """清空订单列表"""
        self.order_table.setRowCount(0)
        self.data_handler.clear_orders()
        
    def export_excel(self):
        """导出Excel"""
        if self.order_table.rowCount() == 0:
            QMessageBox.warning(self, "警告", "没有订单数据可导出！")
            return
            
        # 获取保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存Excel文件", f"派单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel文件 (*.xlsx)"
        )
        
        if file_path:
            try:
                # 收集订单数据
                orders = []
                for row in range(self.order_table.rowCount()):
                    order = {
                        "订单编号": self.order_table.item(row, 0).text(),
                        "商品序号组合": self.order_table.item(row, 1).text(),
                        "商品单价": float(self.order_table.item(row, 2).text()),
                        "数量": int(self.order_table.item(row, 3).text()),
                        "单个商品金额": float(self.order_table.item(row, 4).text()),
                        "订单总金额": float(self.order_table.item(row, 5).text()),
                    }
                    orders.append(order)
                
                # 导出Excel
                self.excel_handler.export_orders(orders, file_path)
                QMessageBox.information(self, "成功", f"Excel文件已导出到：{file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败：{str(e)}")
