# 桌面派单系统 - 深度优化版

## 功能概述

这是一个基于 Python + PyQt5 开发的桌面派单系统，专门用于商品组合订单的生成和管理。系统支持多种商品分类规则和灵活的组合方式。

## 核心功能

### 1. 商品基础信息管理
- 支持从Excel/CSV文件导入商品信息
- 支持手动输入商品信息
- 商品信息包含：序号、单价

### 2. 商品组合分类系统
系统提供三种分类规则：

#### 奇数分类
- 筛选序号为奇数的商品（1、3、5、7...）
- 归入"奇数池"

#### 偶数分类
- 筛选序号为偶数的商品（2、4、6、8...）
- 归入"偶数池"

#### 间隔2分类
- 按序号间隔规律分组：
  - 组1：1、4、7、10、13...
  - 组2：2、5、8、11、14...
  - 组3：3、6、9、12、15...

### 3. 商品池展示功能
- 点击"生成商品池"按钮，根据选择的分类规则自动分组
- 每个商品池显示商品序号和单价
- 支持多选商品进行组合

### 4. 订单组合选择功能

#### 自动组合
- **两两组合**：自动将商品按2个一组进行组合
- **三三组合**：自动将商品按3个一组进行组合
- **剩余处理**：不足组合数量的商品单独成组

#### 手动组合
- 从商品池中多选商品
- 点击"手动组合选中商品"创建自定义组合

### 5. 数量设置与订单生成
- 为每个组合设置下单数量（默认1，可调整）
- 自动计算组合单价和订单总金额
- 支持批量生成订单

### 6. 订单管理
- 订单信息包含：
  - 订单编号（自动递增：ORD001、ORD002...）
  - 商品序号组合（如"1+3"、"2+4+6"）
  - 组合单价
  - 数量
  - 单个商品金额
  - 订单总金额
- 实时统计订单数量和总金额
- 支持清空订单列表

### 7. Excel导出功能
- 导出包含完整订单信息的Excel文件
- 文件包含派单日期和时间
- 自动格式化和美化

## 使用流程

### 步骤1：导入商品信息
1. 点击"选择商品信息文件"导入Excel/CSV文件
2. 或点击"手动输入商品信息"使用示例数据

### 步骤2：选择分类规则
1. 在"商品组合分类"下拉框中选择分类方式
2. 点击"生成商品池"查看分类结果

### 步骤3：生成商品组合
1. 选择组合类型（两两组合/三三组合）
2. 点击"自动组合"进行自动分组
3. 或手动选择商品后点击"手动组合选中商品"

### 步骤4：设置数量并生成订单
1. 在组合结果表格中设置每个组合的数量
2. 点击"生成所有订单"创建订单
3. 查看订单列表和统计信息

### 步骤5：导出结果
1. 点击"导出Excel"保存订单信息
2. 选择保存路径和文件名

## 技术特性

- **界面框架**：PyQt5
- **数据处理**：pandas
- **Excel处理**：openpyxl
- **多选支持**：QListWidget多选模式
- **实时计算**：动态更新金额和统计信息
- **错误处理**：完善的异常处理和用户提示

## 安装要求

```bash
pip install PyQt5 openpyxl pandas
```

## 运行方式

```bash
python main.py
```

或运行测试版本：

```bash
python test_dispatch_system.py
```

## 文件结构

```
├── main.py              # 主程序入口
├── ui_main.py           # 主界面UI模块
├── data_handler.py      # 数据处理模块
├── excel_handler.py     # Excel处理模块
├── test_dispatch_system.py  # 测试程序
├── requirements.txt     # 依赖包列表
└── README.md           # 说明文档
```

## 更新日志

### v2.0 - 深度优化版
- 新增商品组合分类系统（奇数/偶数/间隔2）
- 新增商品池展示功能
- 新增自动组合和手动组合功能
- 新增订单统计信息
- 优化界面布局和用户体验
- 完善错误处理和用户提示

### v1.0 - 基础版
- 基本的商品信息管理
- 简单的订单生成
- Excel导出功能
