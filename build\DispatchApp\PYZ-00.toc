('D:\\work\\python_work\\Desktop Order Dispatch '
 'System\\build\\DispatchApp\\PYZ-00.pyz',
 [('PyQt5',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\soft\\python3.10\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support', 'D:\\soft\\python3.10\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess',
   'D:\\soft\\python3.10\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\soft\\python3.10\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression', 'D:\\soft\\python3.10\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\soft\\python3.10\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\soft\\python3.10\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\soft\\python3.10\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\soft\\python3.10\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\soft\\python3.10\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\soft\\python3.10\\lib\\ast.py', 'PYMODULE'),
  ('asyncio', 'D:\\soft\\python3.10\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.base_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\soft\\python3.10\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\soft\\python3.10\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\soft\\python3.10\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\soft\\python3.10\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\soft\\python3.10\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\soft\\python3.10\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\soft\\python3.10\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks', 'D:\\soft\\python3.10\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.log', 'D:\\soft\\python3.10\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.mixins',
   'D:\\soft\\python3.10\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\soft\\python3.10\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\soft\\python3.10\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\soft\\python3.10\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\soft\\python3.10\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\soft\\python3.10\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\soft\\python3.10\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\soft\\python3.10\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks', 'D:\\soft\\python3.10\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.threads',
   'D:\\soft\\python3.10\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\soft\\python3.10\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\soft\\python3.10\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\soft\\python3.10\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\soft\\python3.10\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\soft\\python3.10\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\soft\\python3.10\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\soft\\python3.10\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\soft\\python3.10\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\soft\\python3.10\\lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'D:\\soft\\python3.10\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\soft\\python3.10\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\soft\\python3.10\\lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\soft\\python3.10\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\soft\\python3.10\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'D:\\soft\\python3.10\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\soft\\python3.10\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\soft\\python3.10\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\soft\\python3.10\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\soft\\python3.10\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian',
   'D:\\soft\\python3.10\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\soft\\python3.10\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('data_handler',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\data_handler.py',
   'PYMODULE'),
  ('dataclasses', 'D:\\soft\\python3.10\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\soft\\python3.10\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal', 'D:\\soft\\python3.10\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\soft\\python3.10\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\soft\\python3.10\\lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\soft\\python3.10\\lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\soft\\python3.10\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\soft\\python3.10\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\soft\\python3.10\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\soft\\python3.10\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\soft\\python3.10\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\soft\\python3.10\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset', 'D:\\soft\\python3.10\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\soft\\python3.10\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\soft\\python3.10\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors', 'D:\\soft\\python3.10\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser',
   'D:\\soft\\python3.10\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\soft\\python3.10\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header', 'D:\\soft\\python3.10\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\soft\\python3.10\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\soft\\python3.10\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message', 'D:\\soft\\python3.10\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\soft\\python3.10\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\soft\\python3.10\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime',
   'D:\\soft\\python3.10\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils', 'D:\\soft\\python3.10\\lib\\email\\utils.py', 'PYMODULE'),
  ('et_xmlfile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('excel_handler',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\excel_handler.py',
   'PYMODULE'),
  ('fileinput', 'D:\\soft\\python3.10\\lib\\fileinput.py', 'PYMODULE'),
  ('fnmatch', 'D:\\soft\\python3.10\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\soft\\python3.10\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\soft\\python3.10\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\soft\\python3.10\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\soft\\python3.10\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\soft\\python3.10\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\soft\\python3.10\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\soft\\python3.10\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\soft\\python3.10\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\soft\\python3.10\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\soft\\python3.10\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities', 'D:\\soft\\python3.10\\lib\\html\\entities.py', 'PYMODULE'),
  ('http', 'D:\\soft\\python3.10\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client', 'D:\\soft\\python3.10\\lib\\http\\client.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\soft\\python3.10\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server', 'D:\\soft\\python3.10\\lib\\http\\server.py', 'PYMODULE'),
  ('importlib',
   'D:\\soft\\python3.10\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\soft\\python3.10\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\soft\\python3.10\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\soft\\python3.10\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\soft\\python3.10\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\soft\\python3.10\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\soft\\python3.10\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\soft\\python3.10\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\soft\\python3.10\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\soft\\python3.10\\lib\\inspect.py', 'PYMODULE'),
  ('json', 'D:\\soft\\python3.10\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder', 'D:\\soft\\python3.10\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.encoder', 'D:\\soft\\python3.10\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\soft\\python3.10\\lib\\json\\scanner.py', 'PYMODULE'),
  ('logging', 'D:\\soft\\python3.10\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\soft\\python3.10\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\soft\\python3.10\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\soft\\python3.10\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\soft\\python3.10\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\soft\\python3.10\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\soft\\python3.10\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\soft\\python3.10\\lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\soft\\python3.10\\lib\\optparse.py', 'PYMODULE'),
  ('pandas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._random',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\_random.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.dtype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.dtype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\dtype.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\methods.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.latex',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\latex.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._str_methods',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_str_methods.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib', 'D:\\soft\\python3.10\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\soft\\python3.10\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\soft\\python3.10\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\soft\\python3.10\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\soft\\python3.10\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\soft\\python3.10\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\soft\\python3.10\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\soft\\python3.10\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\soft\\python3.10\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\soft\\python3.10\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pytz',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue', 'D:\\soft\\python3.10\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\soft\\python3.10\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\soft\\python3.10\\lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\soft\\python3.10\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\soft\\python3.10\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\soft\\python3.10\\lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\soft\\python3.10\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\soft\\python3.10\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\soft\\python3.10\\lib\\signal.py', 'PYMODULE'),
  ('six',
   'D:\\work\\python_work\\Desktop Order Dispatch '
   'System\\venv_auto\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket', 'D:\\soft\\python3.10\\lib\\socket.py', 'PYMODULE'),
  ('socketserver', 'D:\\soft\\python3.10\\lib\\socketserver.py', 'PYMODULE'),
  ('sqlite3', 'D:\\soft\\python3.10\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\soft\\python3.10\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump', 'D:\\soft\\python3.10\\lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('ssl', 'D:\\soft\\python3.10\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\soft\\python3.10\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\soft\\python3.10\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\soft\\python3.10\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\soft\\python3.10\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\soft\\python3.10\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\soft\\python3.10\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\soft\\python3.10\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\soft\\python3.10\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\soft\\python3.10\\lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\soft\\python3.10\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\soft\\python3.10\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\soft\\python3.10\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'D:\\soft\\python3.10\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\soft\\python3.10\\lib\\typing.py', 'PYMODULE'),
  ('ui_main',
   'D:\\work\\python_work\\Desktop Order Dispatch System\\ui_main.py',
   'PYMODULE'),
  ('unittest', 'D:\\soft\\python3.10\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest._log', 'D:\\soft\\python3.10\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.async_case',
   'D:\\soft\\python3.10\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case', 'D:\\soft\\python3.10\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest.loader',
   'D:\\soft\\python3.10\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main', 'D:\\soft\\python3.10\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\soft\\python3.10\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\soft\\python3.10\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\soft\\python3.10\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\soft\\python3.10\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util', 'D:\\soft\\python3.10\\lib\\unittest\\util.py', 'PYMODULE'),
  ('urllib', 'D:\\soft\\python3.10\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error', 'D:\\soft\\python3.10\\lib\\urllib\\error.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\soft\\python3.10\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib.request',
   'D:\\soft\\python3.10\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\soft\\python3.10\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'D:\\soft\\python3.10\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\soft\\python3.10\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\soft\\python3.10\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\soft\\python3.10\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.dom', 'D:\\soft\\python3.10\\lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\soft\\python3.10\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\soft\\python3.10\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\soft\\python3.10\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\soft\\python3.10\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax', 'D:\\soft\\python3.10\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\soft\\python3.10\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'D:\\soft\\python3.10\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client', 'D:\\soft\\python3.10\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('zipfile', 'D:\\soft\\python3.10\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\soft\\python3.10\\lib\\zipimport.py', 'PYMODULE')])
