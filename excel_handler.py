#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel处理模块
"""

from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
from datetime import datetime

class ExcelHandler:
    """Excel处理类"""
    
    def __init__(self):
        pass
    
    def export_orders(self, orders, file_path):
        """导出订单到Excel文件
        
        Args:
            orders (list): 订单列表
            file_path (str): 文件保存路径
        """
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "派单记录"
        
        # 设置样式
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')
        
        data_font = Font(name='微软雅黑', size=10)
        data_alignment = Alignment(horizontal='center', vertical='center')
        
        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 添加标题和日期时间
        current_time = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        ws.merge_cells('A1:F1')
        ws['A1'] = f"派单记录 - {current_time}"
        ws['A1'].font = Font(name='微软雅黑', size=14, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
        
        # 设置表头
        headers = ['订单编号', '商品序号组合', '商品单价', '数量', '单个商品金额', '订单总金额']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border
        
        # 添加数据
        for row_idx, order in enumerate(orders, 4):
            ws.cell(row=row_idx, column=1, value=order['订单编号']).font = data_font
            ws.cell(row=row_idx, column=2, value=order['商品序号组合']).font = data_font
            ws.cell(row=row_idx, column=3, value=order['商品单价']).font = data_font
            ws.cell(row=row_idx, column=4, value=order['数量']).font = data_font
            ws.cell(row=row_idx, column=5, value=order['单个商品金额']).font = data_font
            ws.cell(row=row_idx, column=6, value=order['订单总金额']).font = data_font
            
            # 设置对齐和边框
            for col in range(1, 7):
                cell = ws.cell(row=row_idx, column=col)
                cell.alignment = data_alignment
                cell.border = border
        
        # 添加统计信息
        if orders:
            total_orders = len(orders)
            total_amount = sum(order['订单总金额'] for order in orders)
            
            stats_row = len(orders) + 5
            ws.cell(row=stats_row, column=1, value="统计信息:").font = Font(name='微软雅黑', size=11, bold=True)
            ws.cell(row=stats_row + 1, column=1, value=f"总订单数: {total_orders}").font = data_font
            ws.cell(row=stats_row + 2, column=1, value=f"总金额: {total_amount:.2f}").font = data_font
        
        # 调整列宽
        column_widths = [15, 20, 12, 8, 15, 15]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width
        
        # 保存文件
        wb.save(file_path)

    def export_current_orders(self, orders, file_path):
        """导出本次订单到Excel文件

        Args:
            orders (list): 本次订单列表
            file_path (str): 文件保存路径
        """
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "本次派单记录"

        # 设置样式
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='2196F3', end_color='2196F3', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')

        data_font = Font(name='微软雅黑', size=10)
        data_alignment = Alignment(horizontal='center', vertical='center')

        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 添加标题和日期时间
        current_time = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        ws.merge_cells('A1:G1')
        ws['A1'] = f"本次派单记录 - {current_time}"
        ws['A1'].font = Font(name='微软雅黑', size=14, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

        # 设置表头
        headers = ['订单编号', '商品序号组合', '组合单价', '数量', '单个商品金额', '订单总金额']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border

        # 添加数据
        for row_idx, order in enumerate(orders, 4):
            ws.cell(row=row_idx, column=1, value=order['订单编号']).font = data_font
            ws.cell(row=row_idx, column=2, value=order['商品序号组合']).font = data_font
            ws.cell(row=row_idx, column=3, value=order['组合单价']).font = data_font
            ws.cell(row=row_idx, column=4, value=order['数量']).font = data_font
            ws.cell(row=row_idx, column=5, value=order['单个商品金额']).font = data_font
            ws.cell(row=row_idx, column=6, value=order['订单总金额']).font = data_font

            # 设置对齐和边框
            for col in range(1, 7):
                cell = ws.cell(row=row_idx, column=col)
                cell.alignment = data_alignment
                cell.border = border

        # 添加统计信息
        if orders:
            total_orders = len(orders)
            total_amount = sum(order['订单总金额'] for order in orders)

            stats_row = len(orders) + 5
            ws.cell(row=stats_row, column=1, value="本次统计:").font = Font(name='微软雅黑', size=11, bold=True)
            ws.cell(row=stats_row + 1, column=1, value=f"本次订单数: {total_orders}").font = data_font
            ws.cell(row=stats_row + 2, column=1, value=f"本次总金额: {total_amount:.2f}").font = data_font

        # 调整列宽
        column_widths = [15, 20, 12, 8, 15, 15]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width

        # 保存文件
        wb.save(file_path)

    def export_history_orders(self, orders, file_path):
        """导出历史订单到Excel文件（支持追加到新工作表）

        Args:
            orders (list): 历史订单列表
            file_path (str): 文件保存路径
        """
        from openpyxl import load_workbook
        import os

        # 检查文件是否存在，决定是创建新文件还是追加工作表
        if os.path.exists(file_path):
            try:
                # 文件存在，加载现有工作簿
                wb = load_workbook(file_path)
                print(f"加载现有Excel文件: {file_path}")
            except Exception as e:
                print(f"加载现有文件失败，创建新文件: {e}")
                wb = Workbook()
        else:
            # 文件不存在，创建新工作簿
            wb = Workbook()
            print(f"创建新Excel文件: {file_path}")

        # 创建新工作表，使用时间戳命名
        sheet_name = f"历史汇总_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        if sheet_name in wb.sheetnames:
            # 如果工作表名已存在，添加序号
            counter = 1
            while f"{sheet_name}_{counter}" in wb.sheetnames:
                counter += 1
            sheet_name = f"{sheet_name}_{counter}"

        ws = wb.create_sheet(title=sheet_name)
        print(f"创建新工作表: {sheet_name}")

        # 设置样式
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='4CAF50', end_color='4CAF50', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')

        data_font = Font(name='微软雅黑', size=10)
        data_alignment = Alignment(horizontal='center', vertical='center')

        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # 添加标题和导出时间
        export_time = datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')
        ws.merge_cells('A1:H1')
        ws['A1'] = f"历史派单汇总 - 导出时间: {export_time}"
        ws['A1'].font = Font(name='微软雅黑', size=14, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center')

        # 设置表头
        headers = ['订单编号', '商品序号组合', '组合单价', '数量', '单个商品金额', '订单总金额', '是否剩余商品']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = border

        # 按生成批次分组数据
        current_row = 4
        batch_number = 1

        # 简单按订单编号分组（假设连续的订单编号为同一批次）
        batches = self._group_orders_by_batch(orders)

        for batch_orders in batches:
            # 添加批次分隔行（包含生成日期时间）
            separator_row = current_row
            ws.merge_cells(f'A{separator_row}:G{separator_row}')

            # 获取该批次的生成时间（使用第一个订单的创建时间）
            batch_time = batch_orders[0].get('创建时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            if isinstance(batch_time, str):
                display_time = batch_time
            else:
                display_time = batch_time.strftime('%Y-%m-%d %H:%M:%S')

            separator_cell = ws.cell(row=separator_row, column=1,
                                   value=f"第{batch_number}批次订单 - 生成时间: {display_time}")
            separator_cell.font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
            separator_cell.alignment = Alignment(horizontal='center', vertical='center')
            separator_cell.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

            # 设置分隔行边框
            for col in range(1, 8):
                cell = ws.cell(row=separator_row, column=col)
                cell.border = border

            current_row += 1

            # 添加批次数据
            for order in batch_orders:
                ws.cell(row=current_row, column=1, value=order['订单编号']).font = data_font
                ws.cell(row=current_row, column=2, value=order['商品序号组合']).font = data_font
                ws.cell(row=current_row, column=3, value=order['组合单价']).font = data_font
                ws.cell(row=current_row, column=4, value=order['数量']).font = data_font
                ws.cell(row=current_row, column=5, value=order['单个商品金额']).font = data_font
                ws.cell(row=current_row, column=6, value=order['订单总金额']).font = data_font
                ws.cell(row=current_row, column=7, value="是" if order['是否剩余商品'] else "否").font = data_font

                # 设置对齐和边框
                for col in range(1, 8):
                    cell = ws.cell(row=current_row, column=col)
                    cell.alignment = data_alignment
                    cell.border = border

                current_row += 1

            batch_number += 1

        # 添加统计信息
        if orders:
            total_orders = len(orders)
            total_amount = sum(order['订单总金额'] for order in orders)
            remainder_orders = sum(1 for order in orders if order.get('是否剩余商品', False))
            normal_orders = total_orders - remainder_orders

            # 获取日期范围
            dates = [order['创建时间'] for order in orders if order['创建时间']]
            date_range = f"{min(dates)} 至 {max(dates)}" if dates else "无数据"

            stats_row = len(orders) + 5
            ws.cell(row=stats_row, column=1, value="历史汇总统计:").font = Font(name='微软雅黑', size=11, bold=True)
            ws.cell(row=stats_row + 1, column=1, value=f"总订单数: {total_orders}").font = data_font
            ws.cell(row=stats_row + 2, column=1, value=f"总金额: {total_amount:.2f}").font = data_font
            ws.cell(row=stats_row + 3, column=1, value=f"正常组合订单: {normal_orders}").font = data_font
            ws.cell(row=stats_row + 4, column=1, value=f"剩余商品订单: {remainder_orders}").font = data_font
            ws.cell(row=stats_row + 5, column=1, value=f"时间范围: {date_range}").font = data_font

        # 调整列宽
        column_widths = [15, 20, 12, 8, 15, 15, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[chr(64 + col)].width = width

        # 删除默认的空工作表（如果存在且不是我们创建的工作表）
        if "Sheet" in wb.sheetnames and len(wb.sheetnames) > 1:
            wb.remove(wb["Sheet"])
            print("已删除默认空工作表")

        # 保存文件
        wb.save(file_path)
        print(f"历史订单已保存到: {file_path}, 工作表: {sheet_name}")

    def _group_orders_by_batch(self, orders):
        """按批次分组订单（简单实现：按订单编号连续性分组）"""
        if not orders:
            return []

        batches = []
        current_batch = []

        for i, order in enumerate(orders):
            if i == 0:
                current_batch = [order]
            else:
                # 简单的批次判断：如果订单编号不连续，则认为是新批次
                prev_order_num = int(orders[i-1]['订单编号'].replace('ORD', ''))
                curr_order_num = int(order['订单编号'].replace('ORD', ''))

                if curr_order_num - prev_order_num > 1:
                    # 新批次
                    batches.append(current_batch)
                    current_batch = [order]
                else:
                    current_batch.append(order)

        # 添加最后一个批次
        if current_batch:
            batches.append(current_batch)

        return batches
    
    def create_sample_data_template(self, file_path):
        """创建商品信息模板文件
        
        Args:
            file_path (str): 文件保存路径
        """
        wb = Workbook()
        ws = wb.active
        ws.title = "商品信息模板"
        
        # 设置表头
        headers = ['序号', '单价']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(name='微软雅黑', size=12, bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center')
        
        # 添加示例数据
        sample_data = [
            (1, 10.0),
            (2, 15.0),
            (3, 20.0),
            (4, 25.0),
            (5, 30.0),
        ]
        
        for row_idx, (seq, price) in enumerate(sample_data, 2):
            ws.cell(row=row_idx, column=1, value=seq)
            ws.cell(row=row_idx, column=2, value=price)
        
        # 调整列宽
        ws.column_dimensions['A'].width = 10
        ws.column_dimensions['B'].width = 12
        
        # 保存文件
        wb.save(file_path)
